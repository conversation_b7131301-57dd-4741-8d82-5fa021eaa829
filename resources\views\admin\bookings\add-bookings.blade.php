@include('layout.admin-header')
{{-- @include('layout.scripts') --}}

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Add Booking</h6>
                            <span class="small"><mark>Enter check-in and check-out dates first to see available
                                    rooms</mark></span>
                            <!--vince<a href="{{ route('admin.bookings') }}" class="btn btn-danger float-end">BACK</a>-->
                        </div>
                        <div class="card-body">


                            <!-- Include Select2 CSS -->
                            <link href="{{ asset('assets/css/select2.min.css') }}" rel="stylesheet" />
                            <!-- Include Select2 JS -->
                            <script src="{{ asset('assets/js/select2.min.js') }}" defer></script>
                            {{-- Form to Create Booking --}}
                            <form action="{{ route('admin.booking.store') }}" method="POST">
                                @csrf
                                <div class="row">
                                    <div class="form-group col-md-6">
                                        <label for="guest_info">Select Guest</label>
                                        <select class="form-control select2-guest" id="guest_info" name="guest_info"
                                            style="width: 100%;">
                                            <option value="">-- Select Guest --</option>
                                            <!-- Options will be dynamically added here -->
                                        </select>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="checkin_date">Check-In Date</label>
                                        <input type="text" class="form-control" id="checkin_date" name="checkin_date"
                                            required autocomplete="off">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="checkout_date">Check-Out Date <span id="no_of_days" class="bg-warning small" style="border-radius:3px;padding:3px;">1 day</span></span></label>
                                        <input type="text" class="form-control" id="checkout_date"
                                            name="checkout_date" required autocomplete="off">
                                    </div>


                                    <div class="form-group col-md-3">
                                        <label for="guest_first_name">First Name</label>
                                        <input class="form-control" id="guest_first_name" name="guest_first_name"
                                        autocomplete="off" placeholder="*" required>
                                        <input type="hidden" id="guest_id" name="guest_id">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="guest_last_name">Last Name</label>
                                        <input class="form-control" id="guest_last_name" name="guest_last_name"
                                        autocomplete="off" placeholder="*" required>
                                    </div>



                                    <div class="form-group col-md-3">
                                        <!--vince<div class="btn-group">-->
                                        <label for="filter-dropdown">Room Type</label><?php //vince added label
                                        ?>
                                        <button type="button" class="btn btn-primary dropdown-toggle" style="min-width:100%;"
                                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                                            id="filter-dropdown">
                                            Filter by Room Type
                                        </button>
                                        <div class="dropdown-menu" style="min-width:100%;">
                                            <a class="dropdown-item filter-btn active" data-filter="All"
                                                href="#">All</a>
                                            @foreach ($room_types as $room_Type)
                                                <a class="dropdown-item filter-btn" data-filter="{{ $room_Type->name }}"
                                                    href="#">{{ ucfirst($room_Type->name) }}</a>
                                            @endforeach
                                        </div>
                                        <!--vince</div>-->
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="room_id">Room<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span></label>

                                        <select class="form-control select2-room" id="room_id" name="room_id" style="width: 100%;">
                                            <option value="">Select Room</option>
                                        </select>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="guest_phone">Phone</label>
                                        <input class="form-control" id="guest_phone" name="guest_phone"
                                            autocomplete="off" placeholder="*" required>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="guest_email">Email</label>
                                        <input class="form-control" id="guest_email" name="guest_email"
                                        autocomplete="off" placeholder="---">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="guest_count">Number of Guests</label>
                                        <select class="form-control" id="guest_count" name="guest_count" required>
                                            <option value="">Select Guest Count</option>
                                            @for($i = 1; $i <= 10; $i++)
                                                <option value="{{ $i }}">{{ $i }} Guest{{ $i > 1 ? 's' : '' }}</option>
                                            @endfor
                                        </select>
                                        <small class="form-text text-muted">Extra charges may apply for guests exceeding room capacity</small>
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="room_price">Room Rate</label>
                                        <input type="text" id="room_price" name="room_price" class="form-control text-right"
                                            readonly>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="total_price_visible">Sub-Total</label>
                                        <input type="text" id="total_price_visible" name="total_price_visible" class="form-control text-right" style="font-size:1.2em;"
                                            readonly>
                                        <input type="hidden" id="total_price" name="total_price" class="form-control text-right"
                                            readonly><!--vince added-->
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="status">Booking Status<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span></label>
                                        <select class="form-control" id="booking_status" name="booking_status">
                                            <option value="booked">Booked</option>
                                            <option value="pending">Pending</option>
                                            <option value="checked_in">Checked In</option>
                                            <option value="checked_out">Checked Out</option>
                                            <option value="cancelled">Cancelled</option>
                                        </select>
                                    </div>



                                    {{-- Hidden Inputs to track who created this booking --}}
                                    <input type="hidden" name="booked_by"
                                        value="@if (Auth::check() && Auth::user()->email) {{ Auth::user()->email }}@else no_user @endif">

                                    <div class="form-group col-md-12">
                                        <div style="margin-top:30px;text-align:center;">
                                            <button type="submit" class="btn btn-primary">Create Booking</button>
                                        </div>
                                    </div>
                                </div>
                            </form>




                        </div>
                    </div>
                </div>
            </div>


            <!-- End of Main Content -->
            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->


        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>

    <!-- Datetime Picker JS and CSS-->
    <script src="{{ asset('assets/js/datetimepicker.js') }}"></script>

    <script>
        //vince added block
            $(document).ready(function() {
                $('input[required]').each(function() {
                    // Find the label associated with the required input
                    var label = $("label[for='" + $(this).attr('id') + "']");
                    
                    // Append a red asterisk to the label if it doesn't already have one
                    if (label.length && !label.find('.required-asterisk').length) {
                        label.append('<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span>');
                    }
                });
            });
        //vince end
    </script>

    <!-- JavaScript Initialize for Date Picker -->
    <script>
        $(document).ready(function() {
            $('#checkin_date').datetimepicker({
                format: 'Y-m-d H:i', // Full date and time format
                step: 30, // 30 minutes
                value: new Date(), // Set default date and time
                autoclose: true // Automatically close after selecting the date and time
            });
            var tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1); // Add 1 day for tomorrow
            tomorrow.setHours(12, 0, 0, 0); // Set time to 12:00 PM (noon)
            $('#checkout_date').datetimepicker({
                format: 'Y-m-d H:i', // Full date and time format
                step: 30, // 30 minutes
                value: tomorrow,
                autoclose: true // Automatically close after selecting the date and time
            });
            
        });
    

    //{{-- Script that dynamically gets available room options based on checkin_date and checkout_date --}}
    
        $(document).ready(function() {
            var roomData = []; // Store room data here

            // Function to fetch available rooms
            function fetchAvailableRooms(checkinDate, checkoutDate) {
                $.ajax({
                    url: '{{ route('get.available.rooms') }}',
                    method: 'POST',
                    data: {
                        checkin_date: checkinDate,
                        checkout_date: checkoutDate,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        roomData = response; // Store the fetched room data
                        updateRoomOptions(); // Update the room options
                    }
                });
            }

            // Event listener for date change
            $('#checkin_date, #checkout_date').on('change', function() {
                var checkinDate = $('#checkin_date').val();
                var checkoutDate = $('#checkout_date').val();
                if (checkinDate && checkoutDate) {
                    fetchAvailableRooms(checkinDate, checkoutDate);
                }
            });

            function updateRoomOptions() {
                var selectedFilter = $('.dropdown-item.active').data('filter');
                $('#room_id').empty();
                
                $('#filter-dropdown').text(selectedFilter);//vince added

                var filteredRooms = roomData.filter(function(room) {
                    return selectedFilter === 'All' || (room.room_type && room.room_type.name ===
                        selectedFilter);
                });

                if (filteredRooms.length > 0) {
                    $.each(filteredRooms, function(index, room) {
                        const roomTypeName = room.room_type ? room.room_type.name : 'Unknown Room Type';
                        const roomNumber = room.room_number ? room.room_number : 'Undefined';
                        const roomRate = room.room_type ? room.individual_room_rate : 0;

                        $('#room_id').append('<option value="' + room.id + '" data-price="' + roomRate +
                            '">' + roomNumber + ' - ' + roomTypeName + ' ('+ roomRate +')</option>');
                    });
                } else {
                    $('#room_id').append('<option value="">No rooms available</option>');
                }

                calculateTotalPrice(); // Ensure total price is recalculated after options are updated
            }

            function calculateTotalPrice() {
                var totalPrice = 0;
                var selectedRoom = $('#room_id').find(':selected');
                if (selectedRoom.length > 0) {
                    // Get the room rate and calculate total price
                    const roomRate = parseFloat(selectedRoom.data('price')) || 0;
                    const checkInDate = new Date($('#checkin_date').val());
                    const checkOutDate = new Date($('#checkout_date').val());
                    
                    // Calculate time difference in milliseconds
                    const timeDiff = checkOutDate - checkInDate;

                    // Calculate the difference in hours
                    const hoursDiff = timeDiff / (1000 * 60 * 60);

                    // Calculate the number of full days (round up)
                    let daysDiff = Math.ceil(hoursDiff / 24);//vince orig floor

                    // If more than 24 hours but less than a full additional day, add one extra day (CHANGED TO 0)
                    if (hoursDiff % 24 >= 1) {
                        daysDiff += 0;
                    }
                    //vince added block
                    var days = ' day';
                    if (daysDiff > 1){
                        days = " days";
                    }
                    $('#no_of_days').text(daysDiff + days);
                    //vince end
                    // Calculate total price based on full days
                    totalPrice = roomRate * daysDiff;

                    // Update the room rate input field and total price input field
                    $('#room_price').val(roomRate.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));//vince orig .toFixed(2)
                    $('#total_price_visible').val(totalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));//vince orig .toFixed(2)
                    $('#total_price').val(totalPrice.toFixed(2));
                } else {
                    $('#room_price').val(''); // Clear the room rate field if no room is selected
                    $('#total_price').val(''); // Clear the total price field if no room is selected
                    $('#total_price_visible').val('');
                }
            }



            // Handle dropdown item click
            $(document).on('click', '.dropdown-item', function() {
                $('.dropdown-item').removeClass('active');
                $(this).addClass('active');
                updateRoomOptions(); // Update room options based on selected filter
            });

            // Event listener for room selection change
            $('#room_id').on('change', function() {
                calculateTotalPrice();
            });

            // Event listener for date change
            $('#checkin_date, #checkout_date').on('change', function() {
                calculateTotalPrice();
            });
            //vince added block
            var defaultcheckinDate = $('#checkin_date').val();
            var defaultcheckoutDate = $('#checkout_date').val();
            fetchAvailableRooms(defaultcheckinDate, defaultcheckoutDate);
            //vince end

            // Initial call to set default filter and calculate total price
            updateRoomOptions();
            
        });
    </script>
    <!-- Initialize Select2 and Handle Guest Information -->
    <script>
        $(document).ready(function() {
            // Initialize Select2 for room selection
            $('.select2-room').select2({
                placeholder: 'Select a Room',
                allowClear: true
            });

            // Room selection change event to recalculate price
            /* vince $('.select2-room').on('change', function() {
                calculateTotalPrice(); // Reuse your existing function
            }); */
            //vince added block
            $('#room_id').on('select2:select', function(e) {
                calculateTotalPrice();
            });
            //vince end
            $('.select2-guest').select2({
                //vince tags: true, // Allows users to add new items
                placeholder: 'Select Guest',
                allowClear: true,
                ajax: {
                    url: "{{ route('admin.ajax.getGuests') }}", // Your API endpoint
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term // Query parameter for search
                        };
                    },
                    processResults: function(data) {
                        // Transform the API response into the format required by Select2
                        return {
                            results: data.map(function(guest) {
                                return {
                                    id: guest.id,
                                    text: guest.first_name + ' ' + guest.last_name,
                                    first_name: guest.first_name,
                                    last_name: guest.last_name,
                                    email: guest.email,
                                    phone: guest.phone
                                };
                            })
                        };
                    },
                    cache: true
                }
            });

            $('#guest_info').on('select2:select', function(e) {
                var data = e.params.data;

                // Assuming data contains guest details like this
                $('#guest_first_name').val(data.first_name || '');
                $('#guest_last_name').val(data.last_name || '');
                $('#guest_email').val(data.email || '');
                $('#guest_email').attr('title', data.email || '');//vince added
                $('#guest_phone').val(data.phone || '');
                $('#guest_id').val(data.id || '');//vince added
            });
        });
    </script>
    

</body>

</html>
