<?php

namespace App\Http\Controllers;

use App\Models\RefundPolicy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class RefundPolicyController extends Controller
{
    /**
     * Display the refund policy settings
     */
    public function index()
    {
        // Check permission
        if (!auth()->user()->hasPermission('settings.manage')) {
            abort(403, 'Unauthorized to view refund policy settings');
        }

        $hotelId = get_current_hotel_id();
        $policy = RefundPolicy::where('hotel_id', $hotelId)->first();

        // Create default policy if none exists
        if (!$policy) {
            $policy = RefundPolicy::createDefaultForHotel($hotelId);
        }

        return view('admin.settings.refund-policy', compact('policy'));
    }

    /**
     * Update the refund policy settings
     */
    public function update(Request $request)
    {
        // Check permission
        if (!auth()->user()->hasPermission('settings.manage')) {
            abort(403, 'Unauthorized to update refund policy settings');
        }

        $request->validate([
            'refunds_enabled' => 'boolean',
            'refund_time_limit_days' => 'required|integer|min:1|max:365',
            'require_approval' => 'boolean',
            'auto_approve_limit' => 'required|numeric|min:0',
            'allow_partial_refunds' => 'boolean',
            'allowed_reasons' => 'required|array|min:1',
            'allowed_reasons.*' => 'required|string|max:100',
            'notify_guest' => 'boolean',
            'notify_admin' => 'boolean',
            'notification_email' => 'nullable|email'
        ]);

        try {
            $hotelId = get_current_hotel_id();
            $policy = RefundPolicy::where('hotel_id', $hotelId)->first();

            if (!$policy) {
                $policy = new RefundPolicy(['hotel_id' => $hotelId]);
            }

            $policy->fill([
                'refunds_enabled' => $request->boolean('refunds_enabled'),
                'refund_time_limit_days' => $request->refund_time_limit_days,
                'require_approval' => $request->boolean('require_approval'),
                'auto_approve_limit' => $request->auto_approve_limit,
                'allow_partial_refunds' => $request->boolean('allow_partial_refunds'),
                'allowed_reasons' => array_filter($request->allowed_reasons),
                'notify_guest' => $request->boolean('notify_guest'),
                'notify_admin' => $request->boolean('notify_admin'),
                'notification_email' => $request->notification_email
            ]);

            $policy->save();

            log_activity('Updated refund policy settings', 'Settings Module');

            return redirect()->back()->with('success', 'Refund policy settings updated successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to update refund policy', [
                'error' => $e->getMessage(),
                'hotel_id' => get_current_hotel_id()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update refund policy settings. Please try again.');
        }
    }

    /**
     * Get refund policy for API
     */
    public function show()
    {
        // Check permission
        if (!auth()->user()->hasPermission('payments.view')) {
            abort(403, 'Unauthorized');
        }

        $hotelId = get_current_hotel_id();
        $policy = RefundPolicy::where('hotel_id', $hotelId)->first();

        if (!$policy) {
            $policy = RefundPolicy::createDefaultForHotel($hotelId);
        }

        return response()->json($policy);
    }

    /**
     * Reset refund policy to defaults
     */
    public function reset()
    {
        // Check permission
        if (!auth()->user()->hasPermission('settings.manage')) {
            abort(403, 'Unauthorized to reset refund policy settings');
        }

        try {
            $hotelId = get_current_hotel_id();
            
            // Delete existing policy
            RefundPolicy::where('hotel_id', $hotelId)->delete();
            
            // Create new default policy
            RefundPolicy::createDefaultForHotel($hotelId);

            log_activity('Reset refund policy to defaults', 'Settings Module');

            return redirect()->back()->with('success', 'Refund policy has been reset to default settings.');

        } catch (\Exception $e) {
            Log::error('Failed to reset refund policy', [
                'error' => $e->getMessage(),
                'hotel_id' => get_current_hotel_id()
            ]);

            return redirect()->back()->with('error', 'Failed to reset refund policy. Please try again.');
        }
    }

    /**
     * Add a new allowed reason
     */
    public function addReason(Request $request)
    {
        // Check permission
        if (!auth()->user()->hasPermission('settings.manage')) {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'reason' => 'required|string|max:100'
        ]);

        try {
            $hotelId = get_current_hotel_id();
            $policy = RefundPolicy::where('hotel_id', $hotelId)->first();

            if (!$policy) {
                $policy = RefundPolicy::createDefaultForHotel($hotelId);
            }

            $reasons = $policy->allowed_reasons ?? [];
            
            if (!in_array($request->reason, $reasons)) {
                $reasons[] = $request->reason;
                $policy->update(['allowed_reasons' => $reasons]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Reason added successfully',
                    'reasons' => $reasons
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Reason already exists'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to add refund reason', [
                'error' => $e->getMessage(),
                'reason' => $request->reason
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add reason'
            ]);
        }
    }

    /**
     * Remove an allowed reason
     */
    public function removeReason(Request $request)
    {
        // Check permission
        if (!auth()->user()->hasPermission('settings.manage')) {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'reason' => 'required|string'
        ]);

        try {
            $hotelId = get_current_hotel_id();
            $policy = RefundPolicy::where('hotel_id', $hotelId)->first();

            if ($policy) {
                $reasons = $policy->allowed_reasons ?? [];
                $reasons = array_values(array_filter($reasons, function($r) use ($request) {
                    return $r !== $request->reason;
                }));
                
                $policy->update(['allowed_reasons' => $reasons]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Reason removed successfully',
                    'reasons' => $reasons
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Policy not found'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to remove refund reason', [
                'error' => $e->getMessage(),
                'reason' => $request->reason
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to remove reason'
            ]);
        }
    }
}
