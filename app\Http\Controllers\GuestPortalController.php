<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Invoice;
use App\Models\Guest;
use App\Models\Hotel;
use App\Models\Room;
use App\Models\RoomType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class GuestPortalController extends Controller
{
    /**
     * Browse all available hotels (Trivago-style)
     */
    public function browseHotels()
    {
        $hotels = Hotel::where('status', 'active')
                      ->with(['rooms.roomType'])
                      ->get();

        return view('guest-portal.browse-hotels', compact('hotels'));
    }

    /**
     * Show the guest portal landing page for a specific hotel
     */
    public function hotelPortal($hotelSlug)
    {
        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        
        return view('guest-portal.hotel-landing', compact('hotel'));
    }

    /**
     * Show guest access form (using email + booking reference or invoice number)
     */
    public function showAccessForm($hotelSlug)
    {
        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        
        return view('guest-portal.access-form', compact('hotel'));
    }

    /**
     * Authenticate guest using email and booking/invoice reference
     */
    public function authenticate(Request $request, $hotelSlug)
    {
        $request->validate([
            'email' => 'required|email',
            'reference' => 'required|string',
            'reference_type' => 'required|in:booking,invoice'
        ]);

        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        
        if ($request->reference_type === 'booking') {
            $booking = Booking::where('hotel_id', $hotel->id)
                ->where('guest_email', $request->email)
                ->where('id', $request->reference)
                ->first();
                
            if (!$booking) {
                return back()->withErrors(['reference' => 'Invalid booking reference or email.']);
            }
            
            // Generate secure session token
            $token = Str::random(60);
            session([
                'guest_portal_token' => $token,
                'guest_portal_hotel_id' => $hotel->id,
                'guest_portal_guest_email' => $request->email,
                'guest_portal_expires' => now()->addHours(24)
            ]);
            
            return redirect()->route('guest-portal.dashboard', $hotel->slug);
        }
        
        if ($request->reference_type === 'invoice') {
            $invoice = Invoice::where('hotel_id', $hotel->id)
                ->whereHas('booking', function($query) use ($request) {
                    $query->where('guest_email', $request->email);
                })
                ->where('id', $request->reference)
                ->first();
                
            if (!$invoice) {
                return back()->withErrors(['reference' => 'Invalid invoice reference or email.']);
            }
            
            // Generate secure session token
            $token = Str::random(60);
            session([
                'guest_portal_token' => $token,
                'guest_portal_hotel_id' => $hotel->id,
                'guest_portal_guest_email' => $request->email,
                'guest_portal_expires' => now()->addHours(24)
            ]);
            
            return redirect()->route('guest-portal.invoice', [$hotel->slug, $invoice->id]);
        }
    }

    /**
     * Show guest dashboard
     */
    public function dashboard($hotelSlug)
    {
        if (!$this->isAuthenticated()) {
            return redirect()->route('guest-portal.access', $hotelSlug);
        }

        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        $guestEmail = session('guest_portal_guest_email');
        
        $bookings = Booking::where('hotel_id', $hotel->id)
            ->where('guest_email', $guestEmail)
            ->with(['room.roomType', 'invoices'])
            ->orderBy('created_at', 'desc')
            ->get();
            
        $invoices = Invoice::where('hotel_id', $hotel->id)
            ->whereHas('booking', function($query) use ($guestEmail) {
                $query->where('guest_email', $guestEmail);
            })
            ->with(['booking.room.roomType', 'payments'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('guest-portal.dashboard', compact('hotel', 'bookings', 'invoices'));
    }

    /**
     * Show room booking form (public - no authentication required)
     */
    public function showBookingForm($hotelSlug)
    {
        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        $roomTypes = RoomType::where('hotel_id', $hotel->id)->get();

        return view('guest-portal.book-room', compact('hotel', 'roomTypes'));
    }

    /**
     * Get available rooms for booking (public API)
     */
    public function getAvailableRooms(Request $request, $hotelSlug)
    {
        $request->validate([
            'checkin' => 'required|date|after_or_equal:today',
            'checkout' => 'required|date|after:checkin',
            'guests' => 'required|integer|min:1|max:10'
        ]);

        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();

        // Get all rooms for this hotel with their room types
        $availableRooms = Room::where('hotel_id', $hotel->id)
            ->whereDoesntHave('bookings', function($query) use ($request) {
                $query->where(function($q) use ($request) {
                    $q->whereBetween('checkin_date', [$request->checkin, $request->checkout])
                      ->orWhereBetween('checkout_date', [$request->checkin, $request->checkout])
                      ->orWhere(function($q2) use ($request) {
                          $q2->where('checkin_date', '<=', $request->checkin)
                             ->where('checkout_date', '>=', $request->checkout);
                      });
                })->whereIn('booking_status', ['booked', 'checked_in']);
            })
            ->with('roomType')
            ->get()
            ->map(function($room) {
                return [
                    'id' => $room->id,
                    'room_number' => $room->room_number,
                    'room_type_name' => $room->roomType->name ?? 'Standard Room',
                    'description' => $room->roomType->description ?? '',
                    'max_capacity' => $room->roomType->max_capacity ?? 4,
                    'standard_capacity' => $room->roomType->standard_capacity ?? 2,
                    'bed_type' => $room->roomType->bed_type ?? 'Double',
                    'price' => $room->individual_room_rate ?? $room->roomType->rate ?? 1000,
                    'amenities' => $room->roomType->amenities ?? []
                ];
            });

        return response()->json([
            'success' => true,
            'rooms' => $availableRooms
        ]);
    }

    /**
     * Store a new booking (public - no authentication required)
     */
    public function storeBooking(Request $request, $hotelSlug)
    {
        $request->validate([
            'guest_first_name' => 'required|string|max:255',
            'guest_last_name' => 'required|string|max:255',
            'guest_email' => 'required|email|max:255',
            'guest_phone' => 'required|string|max:20',
            'guest_count' => 'required|integer|min:1|max:20',
            'room_id' => 'required|exists:rooms,id',
            'checkin_date' => 'required|date|after_or_equal:today',
            'checkout_date' => 'required|date|after:checkin_date',
        ]);

        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        $room = Room::with('roomType')->findOrFail($request->room_id);

        // Check room capacity
        $guestCount = $request->guest_count;
        $roomType = $room->roomType;

        if (!$roomType->canAccommodate($guestCount)) {
            return redirect()->back()->with('error', 'The selected room can accommodate a maximum of ' . $roomType->max_capacity . ' guests.');
        }

        // Calculate total price
        $checkinDate = Carbon::parse($request->checkin_date);
        $checkoutDate = Carbon::parse($request->checkout_date);
        $nights = $checkinDate->diffInDays($checkoutDate);
        $totalPrice = ($room->price ?? $room->roomType->rate ?? 1000) * $nights;

        // Calculate extra guest charges
        $extraGuests = $roomType->getExtraGuestCount($guestCount);
        $extraGuestCharge = 0;

        if ($extraGuests > 0 && $hotel->enable_extra_guest_charges) {
            if ($hotel->extra_guest_charge_type === 'per_night') {
                $extraGuestCharge = $extraGuests * $hotel->extra_guest_charge_amount * $nights;
            } else {
                $extraGuestCharge = $extraGuests * $hotel->extra_guest_charge_amount;
            }
        }

        // Create booking
        $booking = Booking::create([
            'hotel_id' => $hotel->id,
            'guest_first_name' => $request->guest_first_name,
            'guest_last_name' => $request->guest_last_name,
            'guest_email' => $request->guest_email,
            'guest_phone' => $request->guest_phone,
            'guest_count' => $guestCount,
            'extra_guests' => $extraGuests,
            'extra_guest_charge' => $extraGuestCharge,
            'room_id' => $request->room_id,
            'checkin_date' => $request->checkin_date,
            'checkout_date' => $request->checkout_date,
            'total_price' => $totalPrice,
            'booking_status' => 'booked',
            'booked_by' => 'Guest Portal'
        ]);

        // Create invoice for the booking
        $totalInvoiceAmount = $totalPrice + $extraGuestCharge;
        $invoice = Invoice::create([
            'hotel_id' => $hotel->id,
            'booking_id' => $booking->id,
            'total_amount' => $totalInvoiceAmount,
            'invoice_date' => now(),
            'status' => 'pending',
            'created_by' => 'Guest Portal',
            'updated_by' => 'Guest Portal'
        ]);

        // Add extra guest charge as additional charge if applicable
        if ($extraGuestCharge > 0) {
            \App\Models\AdditionalCharge::create([
                'hotel_id' => $hotel->id,
                'invoice_id' => $invoice->id,
                'description' => "Extra guest charge ({$extraGuests} extra guest" . ($extraGuests > 1 ? 's' : '') . ")",
                'amount' => $extraGuestCharge,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Booking created successfully!',
            'booking_id' => $booking->id,
            'invoice_id' => $invoice->id,
            'redirect_url' => route('guest-portal.payment', [$hotelSlug, $invoice->id])
        ]);
    }

    /**
     * Show booking confirmation
     */
    public function bookingConfirmation($hotelSlug, $bookingId)
    {
        if (!$this->isAuthenticated()) {
            return redirect()->route('guest-portal.access', $hotelSlug);
        }

        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        $guestEmail = session('guest_portal_guest_email');
        
        $booking = Booking::where('hotel_id', $hotel->id)
            ->where('guest_email', $guestEmail)
            ->where('id', $bookingId)
            ->with(['room.roomType'])
            ->firstOrFail();

        return view('guest-portal.booking-confirmation', compact('hotel', 'booking'));
    }

    /**
     * Show invoice details
     */
    public function showInvoice($hotelSlug, $invoiceId)
    {
        if (!$this->isAuthenticated()) {
            return redirect()->route('guest-portal.access', $hotelSlug);
        }

        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        $guestEmail = session('guest_portal_guest_email');
        
        $invoice = Invoice::where('hotel_id', $hotel->id)
            ->whereHas('booking', function($query) use ($guestEmail) {
                $query->where('guest_email', $guestEmail);
            })
            ->where('id', $invoiceId)
            ->with(['booking.room.roomType', 'additionalCharges', 'payments'])
            ->firstOrFail();

        return view('guest-portal.invoice', compact('hotel', 'invoice'));
    }

    /**
     * Check if guest is authenticated
     */
    private function isAuthenticated()
    {
        return session('guest_portal_token') && 
               session('guest_portal_expires') && 
               session('guest_portal_expires')->isFuture();
    }

    /**
     * Logout guest
     */
    public function logout($hotelSlug)
    {
        session()->forget([
            'guest_portal_token',
            'guest_portal_hotel_id',
            'guest_portal_guest_email',
            'guest_portal_expires'
        ]);

        return redirect()->route('guest-portal.hotel', $hotelSlug)
            ->with('success', 'You have been logged out successfully.');
    }
}
