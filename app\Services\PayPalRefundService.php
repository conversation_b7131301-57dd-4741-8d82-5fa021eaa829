<?php

namespace App\Services;

use App\Models\Refund;
use App\Models\Payment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

class PayPalRefundService
{
    private $baseUrl;
    private $clientId;
    private $clientSecret;

    public function __construct()
    {
        $config = get_paypal_config();
        $this->baseUrl = $config['base_url'];
        $this->clientId = $config['client_id'];
        $this->clientSecret = $config['client_secret'];
    }

    /**
     * Process a refund through PayPal API
     */
    public function processRefund(Refund $refund)
    {
        try {
            // Validate refund can be processed
            if (!$refund->canBeProcessed()) {
                throw new Exception('Refund cannot be processed in current status: ' . $refund->status);
            }

            // Get the payment details
            $payment = $refund->payment;
            if (!$payment) {
                throw new Exception('Payment not found for refund');
            }

            // Get PayPal capture ID
            $captureId = $this->getPayPalCaptureId($payment);
            if (!$captureId) {
                throw new Exception('PayPal capture ID not found for this payment');
            }

            // Update refund status to processing
            $refund->update([
                'status' => 'processing',
                'processed_by' => auth()->user()->email ?? 'system',
                'processed_at' => now()
            ]);

            // Get access token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                throw new Exception('Failed to get PayPal access token');
            }

            // Prepare refund data
            $refundData = [
                'amount' => [
                    'value' => number_format($refund->refund_amount, 2, '.', ''),
                    'currency_code' => get_paypal_config()['currency']
                ],
                'note_to_payer' => $refund->reason . ($refund->notes ? ' - ' . $refund->notes : '')
            ];

            // Make API call to PayPal
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $accessToken,
                'PayPal-Request-Id' => uniqid('refund_')
            ])->post($this->baseUrl . '/v2/payments/captures/' . $captureId . '/refund', $refundData);

            // Handle response
            if ($response->successful()) {
                $responseData = $response->json();
                
                // Update refund with success data
                $refund->update([
                    'status' => 'completed',
                    'provider_refund_id' => $responseData['id'] ?? null,
                    'provider_response' => $responseData,
                    'processed_at' => now()
                ]);

                Log::info('PayPal refund processed successfully', [
                    'refund_id' => $refund->id,
                    'refund_number' => $refund->refund_number,
                    'amount' => $refund->refund_amount,
                    'paypal_refund_id' => $responseData['id'] ?? null
                ]);

                return [
                    'success' => true,
                    'message' => 'Refund processed successfully',
                    'data' => $responseData
                ];

            } else {
                // Handle API error
                $errorData = $response->json();
                
                $refund->update([
                    'status' => 'failed',
                    'provider_response' => $errorData,
                    'processed_at' => now()
                ]);

                Log::error('PayPal refund failed', [
                    'refund_id' => $refund->id,
                    'refund_number' => $refund->refund_number,
                    'error' => $errorData,
                    'status_code' => $response->status()
                ]);

                $errorMessage = $errorData['details'][0]['description'] ?? 
                               $errorData['message'] ?? 
                               'Unknown error';

                return [
                    'success' => false,
                    'message' => 'Refund failed: ' . $errorMessage,
                    'data' => $errorData
                ];
            }

        } catch (Exception $e) {
            // Update refund status to failed
            $refund->update([
                'status' => 'failed',
                'provider_response' => ['error' => $e->getMessage()],
                'processed_at' => now()
            ]);

            Log::error('PayPal refund exception', [
                'refund_id' => $refund->id,
                'refund_number' => $refund->refund_number,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Refund failed: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Check refund status from PayPal
     */
    public function checkRefundStatus($refundId)
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                throw new Exception('Failed to get PayPal access token');
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json'
            ])->get($this->baseUrl . '/v2/payments/refunds/' . $refundId);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to check refund status',
                'data' => $response->json()
            ];

        } catch (Exception $e) {
            Log::error('PayPal refund status check failed', [
                'refund_id' => $refundId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Error checking refund status: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get PayPal access token
     */
    private function getAccessToken()
    {
        try {
            return get_paypal_access_token();
        } catch (Exception $e) {
            Log::error('Failed to get PayPal access token', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Get PayPal capture ID from payment record
     */
    private function getPayPalCaptureId(Payment $payment)
    {
        // For PayPal, we need to store the capture ID when payment is made
        // This could be in a separate field or extracted from provider response
        
        // Check if we have stored the PayPal capture ID
        if (isset($payment->provider_payment_id)) {
            return $payment->provider_payment_id;
        }

        // If not stored separately, we might need to extract from session or other source
        // For now, return null and handle this case
        return null;
    }

    /**
     * Validate if PayPal refund is possible
     */
    public function canRefund(Payment $payment, $amount)
    {
        // Check if payment method is PayPal
        if (strtolower($payment->payment_method) !== 'paypal') {
            return [
                'can_refund' => false,
                'message' => 'Payment was not made through PayPal'
            ];
        }

        // Check if amount is valid
        if ($amount <= 0 || $amount > $payment->amount) {
            return [
                'can_refund' => false,
                'message' => 'Invalid refund amount'
            ];
        }

        // Check if payment can be refunded (not already fully refunded)
        if (!$payment->canBeRefunded()) {
            return [
                'can_refund' => false,
                'message' => 'Payment has already been fully refunded'
            ];
        }

        // PayPal has a 180-day refund window
        $paymentDate = $payment->payment_date;
        if ($paymentDate->diffInDays(now()) > 180) {
            return [
                'can_refund' => false,
                'message' => 'PayPal refunds are only available within 180 days of payment'
            ];
        }

        return [
            'can_refund' => true,
            'message' => 'Payment can be refunded'
        ];
    }
}
