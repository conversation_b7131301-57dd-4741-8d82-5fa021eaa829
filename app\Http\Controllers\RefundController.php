<?php

namespace App\Http\Controllers;

use App\Models\Refund;
use App\Models\RefundPolicy;
use App\Models\Payment;
use App\Models\Invoice;
use App\Services\PayMongoRefundService;
use App\Services\PayPalRefundService;
use App\Services\RefundNotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RefundController extends Controller
{
    protected $paymongoRefundService;
    protected $paypalRefundService;
    protected $notificationService;

    public function __construct(
        PayMongoRefundService $paymongoRefundService,
        PayPalRefundService $paypalRefundService,
        RefundNotificationService $notificationService
    ) {
        $this->paymongoRefundService = $paymongoRefundService;
        $this->paypalRefundService = $paypalRefundService;
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of refunds
     */
    public function index(Request $request)
    {
        // Check permission
        if (!auth()->user()->hasPermission('payments.refund')) {
            abort(403, 'Unauthorized to view refunds');
        }

        $query = Refund::with(['payment', 'invoice.booking', 'requester', 'approver'])
            ->where('hotel_id', get_current_hotel_id())
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Search by refund number or guest name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('refund_number', 'like', "%{$search}%")
                  ->orWhereHas('invoice.booking', function($booking) use ($search) {
                      $booking->where('guest_first_name', 'like', "%{$search}%")
                              ->orWhere('guest_last_name', 'like', "%{$search}%");
                  });
            });
        }

        $refunds = $query->paginate(20);

        return view('admin.refunds.index', compact('refunds'));
    }

    /**
     * Show the form for creating a new refund
     */
    public function create(Request $request)
    {
        // Check permission
        if (!auth()->user()->hasPermission('payments.refund')) {
            abort(403, 'Unauthorized to create refunds');
        }

        $paymentId = $request->get('payment_id');
        $payment = Payment::with(['invoice.booking', 'refunds'])
            ->where('hotel_id', get_current_hotel_id())
            ->findOrFail($paymentId);

        // Check if payment can be refunded
        if (!$payment->canBeRefunded()) {
            return redirect()->back()->with('error', 'This payment cannot be refunded.');
        }

        // Get refund policy
        $policy = RefundPolicy::where('hotel_id', get_current_hotel_id())->first();
        if (!$policy || !$policy->refunds_enabled) {
            return redirect()->back()->with('error', 'Refunds are not enabled for this hotel.');
        }

        return view('admin.refunds.create', compact('payment', 'policy'));
    }

    /**
     * Store a newly created refund
     */
    public function store(Request $request)
    {
        // Check permission
        if (!auth()->user()->hasPermission('payments.refund')) {
            abort(403, 'Unauthorized to create refunds');
        }

        $request->validate([
            'payment_id' => 'required|exists:payments,id',
            'refund_type' => 'required|in:full,partial',
            'refund_amount' => 'required|numeric|min:0.01',
            'reason' => 'required|string|max:500',
            'notes' => 'nullable|string|max:1000'
        ]);

        try {
            DB::beginTransaction();

            $payment = Payment::where('hotel_id', get_current_hotel_id())
                ->findOrFail($request->payment_id);

            // Validate refund amount
            if ($request->refund_amount > $payment->remainingRefundableAmount) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Refund amount exceeds the remaining refundable amount.');
            }

            // Get refund policy
            $policy = RefundPolicy::where('hotel_id', get_current_hotel_id())->first();
            if (!$policy || !$policy->refunds_enabled) {
                return redirect()->back()->with('error', 'Refunds are not enabled.');
            }

            // Create refund
            $refund = Refund::create([
                'hotel_id' => get_current_hotel_id(),
                'payment_id' => $payment->id,
                'invoice_id' => $payment->invoice_id,
                'refund_number' => Refund::generateRefundNumber(get_current_hotel_id()),
                'refund_amount' => $request->refund_amount,
                'original_amount' => $payment->amount,
                'refund_type' => $request->refund_type,
                'reason' => $request->reason,
                'notes' => $request->notes,
                'payment_method' => $payment->payment_method,
                'requested_by' => auth()->user()->email,
                'requested_at' => now(),
                'status' => $policy->shouldAutoApprove($request->refund_amount) ? 'approved' : 'pending'
            ]);

            // If auto-approved, set approval details
            if ($refund->status === 'approved') {
                $refund->update([
                    'approved_by' => 'system',
                    'approved_at' => now()
                ]);
            }

            DB::commit();

            // Log activity
            log_activity('Refund request created: ' . $refund->refund_number, 'Refund Module');

            // Send notifications
            if ($refund->status === 'approved') {
                $this->notificationService->sendRefundApprovedNotification($refund);
            } else {
                $this->notificationService->sendRefundRequestedNotification($refund);
            }

            $message = $refund->status === 'approved'
                ? 'Refund request created and auto-approved.'
                : 'Refund request created and pending approval.';

            return redirect()->route('admin.refunds.show', $refund)
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create refund', [
                'error' => $e->getMessage(),
                'payment_id' => $request->payment_id
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create refund request. Please try again.');
        }
    }

    /**
     * Display the specified refund
     */
    public function show(Refund $refund)
    {
        // Check permission and hotel access
        if (!auth()->user()->hasPermission('payments.refund') || 
            $refund->hotel_id !== get_current_hotel_id()) {
            abort(403, 'Unauthorized to view this refund');
        }

        $refund->load(['payment', 'invoice.booking', 'requester', 'approver', 'processor', 'rejector']);

        return view('admin.refunds.show', compact('refund'));
    }

    /**
     * Approve a refund
     */
    public function approve(Refund $refund)
    {
        // Check permission and hotel access
        if (!auth()->user()->hasPermission('payments.refund') || 
            $refund->hotel_id !== get_current_hotel_id()) {
            abort(403, 'Unauthorized to approve refunds');
        }

        if (!$refund->canBeApproved()) {
            return redirect()->back()->with('error', 'This refund cannot be approved.');
        }

        $refund->update([
            'status' => 'approved',
            'approved_by' => auth()->user()->email,
            'approved_at' => now()
        ]);

        log_activity('Refund approved: ' . $refund->refund_number, 'Refund Module');

        // Send notification
        $this->notificationService->sendRefundApprovedNotification($refund);

        return redirect()->back()->with('success', 'Refund has been approved.');
    }

    /**
     * Reject a refund
     */
    public function reject(Request $request, Refund $refund)
    {
        // Check permission and hotel access
        if (!auth()->user()->hasPermission('payments.refund') || 
            $refund->hotel_id !== get_current_hotel_id()) {
            abort(403, 'Unauthorized to reject refunds');
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        if (!$refund->canBeRejected()) {
            return redirect()->back()->with('error', 'This refund cannot be rejected.');
        }

        $refund->update([
            'status' => 'rejected',
            'rejected_by' => auth()->user()->email,
            'rejected_at' => now(),
            'notes' => ($refund->notes ? $refund->notes . "\n\n" : '') . 
                      'Rejection reason: ' . $request->rejection_reason
        ]);

        log_activity('Refund rejected: ' . $refund->refund_number, 'Refund Module');

        // Send notification
        $this->notificationService->sendRefundRejectedNotification($refund, $request->rejection_reason);

        return redirect()->back()->with('success', 'Refund has been rejected.');
    }

    /**
     * Process a refund through the payment provider
     */
    public function process(Refund $refund)
    {
        // Check permission and hotel access
        if (!auth()->user()->hasPermission('payments.refund') || 
            $refund->hotel_id !== get_current_hotel_id()) {
            abort(403, 'Unauthorized to process refunds');
        }

        if (!$refund->canBeProcessed()) {
            return redirect()->back()->with('error', 'This refund cannot be processed.');
        }

        try {
            $result = null;

            // Process based on payment method
            switch (strtolower($refund->payment_method)) {
                case 'paymongo':
                    $result = $this->paymongoRefundService->processRefund($refund);
                    break;
                    
                case 'paypal':
                    $result = $this->paypalRefundService->processRefund($refund);
                    break;
                    
                case 'cash':
                    // For cash payments, mark as completed immediately
                    $refund->update([
                        'status' => 'completed',
                        'processed_by' => auth()->user()->email,
                        'processed_at' => now(),
                        'provider_response' => ['method' => 'cash', 'note' => 'Cash refund processed manually']
                    ]);
                    $result = ['success' => true, 'message' => 'Cash refund marked as completed'];
                    break;
                    
                default:
                    return redirect()->back()->with('error', 'Unsupported payment method for refunds.');
            }

            if ($result['success']) {
                log_activity('Refund processed: ' . $refund->refund_number, 'Refund Module');
                return redirect()->back()->with('success', $result['message']);
            } else {
                return redirect()->back()->with('error', $result['message']);
            }

        } catch (\Exception $e) {
            Log::error('Failed to process refund', [
                'refund_id' => $refund->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'Failed to process refund. Please try again.');
        }
    }

    /**
     * Cancel a refund
     */
    public function cancel(Refund $refund)
    {
        // Check permission and hotel access
        if (!auth()->user()->hasPermission('payments.refund') ||
            $refund->hotel_id !== get_current_hotel_id()) {
            abort(403, 'Unauthorized to cancel refunds');
        }

        if (!$refund->canBeCancelled()) {
            return redirect()->back()->with('error', 'This refund cannot be cancelled.');
        }

        $refund->update([
            'status' => 'cancelled',
            'notes' => ($refund->notes ? $refund->notes . "\n\n" : '') .
                      'Cancelled by: ' . auth()->user()->email . ' at ' . now()
        ]);

        log_activity('Refund cancelled: ' . $refund->refund_number, 'Refund Module');

        return redirect()->back()->with('success', 'Refund has been cancelled.');
    }

    /**
     * Check refund eligibility for a payment
     */
    public function checkEligibility(Payment $payment)
    {
        // Check permission and hotel access
        if (!auth()->user()->hasPermission('payments.view') ||
            $payment->hotel_id !== get_current_hotel_id()) {
            abort(403, 'Unauthorized');
        }

        $policy = RefundPolicy::where('hotel_id', get_current_hotel_id())->first();

        $eligibility = [
            'eligible' => false,
            'reasons' => []
        ];

        // Check if refunds are enabled
        if (!$policy || !$policy->refunds_enabled) {
            $eligibility['reasons'][] = 'Refunds are not enabled for this hotel';
            return response()->json($eligibility);
        }

        // Check if payment can be refunded
        if (!$payment->canBeRefunded()) {
            $eligibility['reasons'][] = 'Payment has already been fully refunded';
            return response()->json($eligibility);
        }

        // Check time limit
        $paymentDate = $payment->payment_date;
        $daysSincePayment = $paymentDate->diffInDays(now());

        if ($daysSincePayment > $policy->refund_time_limit_days) {
            $eligibility['reasons'][] = "Refund time limit exceeded ({$policy->refund_time_limit_days} days)";
            return response()->json($eligibility);
        }

        // Additional checks for PayPal (180-day limit)
        if (strtolower($payment->payment_method) === 'paypal' && $daysSincePayment > 180) {
            $eligibility['reasons'][] = 'PayPal refunds are only available within 180 days';
            return response()->json($eligibility);
        }

        $eligibility['eligible'] = true;
        $eligibility['remaining_amount'] = $payment->remainingRefundableAmount;
        $eligibility['policy'] = $policy;

        return response()->json($eligibility);
    }

    /**
     * Get refund statistics
     */
    public function statistics()
    {
        // Check permission
        if (!auth()->user()->hasPermission('payments.refund')) {
            abort(403, 'Unauthorized');
        }

        $hotelId = get_current_hotel_id();

        $stats = [
            'total_refunds' => Refund::where('hotel_id', $hotelId)->count(),
            'pending_refunds' => Refund::where('hotel_id', $hotelId)->where('status', 'pending')->count(),
            'completed_refunds' => Refund::where('hotel_id', $hotelId)->where('status', 'completed')->count(),
            'total_refunded_amount' => Refund::where('hotel_id', $hotelId)
                ->where('status', 'completed')
                ->sum('refund_amount'),
            'refunds_this_month' => Refund::where('hotel_id', $hotelId)
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'refund_amount_this_month' => Refund::where('hotel_id', $hotelId)
                ->where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('refund_amount')
        ];

        return response()->json($stats);
    }
}
