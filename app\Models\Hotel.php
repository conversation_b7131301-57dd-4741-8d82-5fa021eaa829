<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Hotel extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'email',
        'phone',
        'address',
        'tin',
        'description',
        'logo',
        'website',
        'status',
        'settings',
        'trial_ends_at',
        'subscription_ends_at',
        'current_subscription_id',
        'custom_domain',
        'subdomain',
        'brand_color',
        'brand_logo',
        'enable_extra_guest_charges',
        'extra_guest_charge_amount',
        'extra_guest_charge_type'
    ];

    protected $casts = [
        'settings' => 'array',
        'trial_ends_at' => 'datetime',
        'subscription_ends_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($hotel) {
            if (empty($hotel->slug)) {
                $hotel->slug = Str::slug($hotel->name);
            }
        });
    }

    // Relationships
    public function users()
    {
        return $this->belongsToMany(User::class, 'hotel_user')
                    ->withPivot('role', 'is_active')
                    ->withTimestamps();
    }

    public function rooms()
    {
        return $this->hasMany(Room::class);
    }

    public function roomTypes()
    {
        return $this->hasMany(RoomType::class);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function guests()
    {
        return $this->hasMany(Guest::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function services()
    {
        return $this->hasMany(Service::class);
    }

    public function events()
    {
        return $this->hasMany(Event::class);
    }

    public function menus()
    {
        return $this->hasMany(Menu::class);
    }

    public function ingredients()
    {
        return $this->hasMany(Ingredient::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    public function settings()
    {
        return $this->hasMany(Setting::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function currentSubscription()
    {
        return $this->belongsTo(Subscription::class, 'current_subscription_id');
    }

    public function billingInvoices()
    {
        return $this->hasMany(BillingInvoice::class);
    }

    // Helper methods
    public function isActive()
    {
        return $this->status === 'active';
    }

    public function isSuspended()
    {
        return $this->status === 'suspended';
    }

    public function isOnTrialLegacy()
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    public function hasActiveSubscriptionLegacy()
    {
        return $this->subscription_ends_at && $this->subscription_ends_at->isFuture();
    }

    public function getSetting($key, $default = null)
    {
        $settings = $this->settings ?? [];
        return $settings[$key] ?? $default;
    }

    public function setSetting($key, $value)
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $this->settings = $settings;
        $this->save();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeBySlug($query, $slug)
    {
        return $query->where('slug', $slug);
    }

    // Subscription helper methods
    public function hasActiveSubscription()
    {
        return $this->currentSubscription && $this->currentSubscription->isActive();
    }

    public function isOnTrial()
    {
        return $this->currentSubscription && $this->currentSubscription->onTrial();
    }

    public function trialDaysLeft()
    {
        if (!$this->isOnTrial()) {
            return 0;
        }
        return $this->currentSubscription->daysLeftOnTrial();
    }

    public function canAddRoom()
    {
        if (!$this->currentSubscription || !$this->currentSubscription->plan) {
            return false;
        }

        $plan = $this->currentSubscription->plan;
        if ($plan->isUnlimitedRooms()) {
            return true;
        }

        return $this->rooms()->count() < $plan->max_rooms;
    }

    public function canAddUser()
    {
        if (!$this->currentSubscription || !$this->currentSubscription->plan) {
            return false;
        }

        $plan = $this->currentSubscription->plan;
        if ($plan->isUnlimitedUsers()) {
            return true;
        }

        return $this->users()->count() < $plan->max_users;
    }

    public function hasFeature($feature)
    {
        if (!$this->currentSubscription || !$this->currentSubscription->plan) {
            return false;
        }

        return $this->currentSubscription->plan->hasFeature($feature);
    }

    // Branding methods
    public function getBrandColorAttribute($value)
    {
        return $value ?: '#4e73df'; // Default blue color
    }

    public function getCustomDomainUrlAttribute()
    {
        if ($this->custom_domain) {
            return 'https://' . $this->custom_domain;
        }

        if ($this->subdomain) {
            return 'https://' . $this->subdomain . '.hotelmanager.com';
        }

        return null;
    }
}
