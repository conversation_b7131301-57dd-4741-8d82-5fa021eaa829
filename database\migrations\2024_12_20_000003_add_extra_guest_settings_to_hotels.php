<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->boolean('enable_extra_guest_charges')->default(true)->after('brand_logo');
            $table->decimal('extra_guest_charge_amount', 10, 2)->default(50.00)->after('enable_extra_guest_charges');
            $table->string('extra_guest_charge_type')->default('per_night')->after('extra_guest_charge_amount'); // per_night or per_stay
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropColumn(['enable_extra_guest_charges', 'extra_guest_charge_amount', 'extra_guest_charge_type']);
        });
    }
};
