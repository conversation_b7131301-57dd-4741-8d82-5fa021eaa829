@include('layout.admin-header')
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Create Refund Request</h6>
                        </div>
                        
                        <div class="card-body">
                            <!-- Payment Information -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h5>Payment Information</h5>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <strong>Guest:</strong> 
                                                    {{ $payment->invoice->booking->guest_first_name ?? 'N/A' }} 
                                                    {{ $payment->invoice->booking->guest_last_name ?? '' }}<br>
                                                    <strong>Invoice #:</strong> {{ $payment->invoice->id }}<br>
                                                    <strong>Payment Method:</strong> 
                                                    <span class="badge badge-info">{{ ucfirst($payment->payment_method) }}</span>
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>Payment Amount:</strong> {{ $currencySymbol }}{{ number_format($payment->amount, 2) }}<br>
                                                    <strong>Payment Date:</strong> {{ $payment->payment_date->format('M d, Y H:i') }}<br>
                                                    <strong>Remaining Refundable:</strong> 
                                                    <span class="text-success font-weight-bold">
                                                        {{ $currencySymbol }}{{ number_format($payment->remainingRefundableAmount, 2) }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Refund Form -->
                            <form method="POST" action="{{ route('admin.refunds.store') }}" id="refundForm">
                                @csrf
                                <input type="hidden" name="payment_id" value="{{ $payment->id }}">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="refund_type">Refund Type</label>
                                            <select name="refund_type" id="refund_type" class="form-control" required>
                                                <option value="">Select Refund Type</option>
                                                <option value="full" {{ old('refund_type') == 'full' ? 'selected' : '' }}>
                                                    Full Refund ({{ $currencySymbol }}{{ number_format($payment->remainingRefundableAmount, 2) }})
                                                </option>
                                                @if($policy->allow_partial_refunds)
                                                    <option value="partial" {{ old('refund_type') == 'partial' ? 'selected' : '' }}>
                                                        Partial Refund
                                                    </option>
                                                @endif
                                            </select>
                                            @error('refund_type')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="refund_amount">Refund Amount</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">{{ $currencySymbol }}</span>
                                                </div>
                                                <input type="number" name="refund_amount" id="refund_amount" 
                                                       class="form-control" step="0.01" min="0.01" 
                                                       max="{{ $payment->remainingRefundableAmount }}"
                                                       value="{{ old('refund_amount') }}" required>
                                            </div>
                                            <small class="form-text text-muted">
                                                Maximum refundable amount: {{ $currencySymbol }}{{ number_format($payment->remainingRefundableAmount, 2) }}
                                            </small>
                                            @error('refund_amount')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="reason">Refund Reason</label>
                                            <select name="reason" id="reason" class="form-control" required>
                                                <option value="">Select Reason</option>
                                                @foreach($policy->getDefaultReasons() as $reason)
                                                    <option value="{{ $reason }}" {{ old('reason') == $reason ? 'selected' : '' }}>
                                                        {{ $reason }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('reason')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="notes">Additional Notes (Optional)</label>
                                            <textarea name="notes" id="notes" class="form-control" rows="3" 
                                                      placeholder="Any additional information about this refund...">{{ old('notes') }}</textarea>
                                            @error('notes')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Policy Information -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle"></i> Refund Policy Information</h6>
                                            <ul class="mb-0">
                                                <li>Refund time limit: {{ $policy->refund_time_limit_days }} days from payment date</li>
                                                @if($policy->require_approval)
                                                    <li>This refund will require approval before processing</li>
                                                @endif
                                                @if($policy->auto_approve_limit > 0)
                                                    <li>Refunds under {{ $currencySymbol }}{{ number_format($policy->auto_approve_limit, 2) }} are automatically approved</li>
                                                @endif
                                                @if($payment->payment_method === 'paypal')
                                                    <li><strong>PayPal Note:</strong> PayPal refunds are only available within 180 days of payment</li>
                                                @endif
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Submit Refund Request
                                    </button>
                                    <a href="{{ route('admin.refunds') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Refunds
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    @include('layout.admin-logout-modal')

    @include('layout.admin-scripts')

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const refundTypeSelect = document.getElementById('refund_type');
            const refundAmountInput = document.getElementById('refund_amount');
            const maxAmount = {{ $payment->remainingRefundableAmount }};

            refundTypeSelect.addEventListener('change', function() {
                if (this.value === 'full') {
                    refundAmountInput.value = maxAmount.toFixed(2);
                    refundAmountInput.readOnly = true;
                } else if (this.value === 'partial') {
                    refundAmountInput.value = '';
                    refundAmountInput.readOnly = false;
                    refundAmountInput.focus();
                } else {
                    refundAmountInput.value = '';
                    refundAmountInput.readOnly = false;
                }
            });

            // Form validation
            document.getElementById('refundForm').addEventListener('submit', function(e) {
                const amount = parseFloat(refundAmountInput.value);
                
                if (amount <= 0) {
                    e.preventDefault();
                    alert('Refund amount must be greater than 0');
                    return;
                }
                
                if (amount > maxAmount) {
                    e.preventDefault();
                    alert('Refund amount cannot exceed the remaining refundable amount');
                    return;
                }

                if (!confirm('Are you sure you want to submit this refund request?')) {
                    e.preventDefault();
                }
            });
        });
    </script>

</body>

</html>
