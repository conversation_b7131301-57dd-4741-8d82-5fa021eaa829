<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Invoice extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'invoices';
    protected $fillable = ['hotel_id', 'booking_id', 'total_amount', 'invoice_date', 'status', 'created_by', 'updated_by'];

    protected $casts = [
        'status' => 'string',
    ];
    

    public function booking()
    {
        return $this->belongsTo(Booking::class, 'booking_id', 'id');
    }
    public function order()//vince added
    {
        return $this->hasOne(Order::class, 'invoice_id', 'id');
    }

    public function additionalCharges()
    {
        return $this->hasMany(AdditionalCharge::class);
    }

    // Invoice.php
    public function payments()
    {
        return $this->hasMany(Payment::class, 'invoice_id', 'id');
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    public function refunds()
    {
        return $this->hasMany(Refund::class);
    }

    // Helper methods for refunds
    public function canBeRefunded()
    {
        // Check if invoice has payments that can be refunded
        return $this->payments()->whereHas('refunds', function($query) {
            $query->where('status', '!=', 'completed');
        })->exists() || $this->payments()->whereDoesntHave('refunds')->exists();
    }

    public function getTotalRefundedAttribute()
    {
        return $this->refunds()->where('status', 'completed')->sum('refund_amount');
    }

    public function hasRefunds()
    {
        return $this->refunds()->exists();
    }

}
