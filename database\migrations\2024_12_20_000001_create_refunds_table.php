<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('refunds', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hotel_id')->constrained()->onDelete('cascade');
            $table->foreignId('payment_id')->constrained()->onDelete('cascade');
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->string('refund_number')->unique(); // REF-YYYYMMDD-XXXX format
            
            // Refund details
            $table->decimal('refund_amount', 10, 2);
            $table->decimal('original_amount', 10, 2); // Store original payment amount
            $table->enum('refund_type', ['full', 'partial'])->default('full');
            $table->text('reason'); // Reason for refund
            $table->text('notes')->nullable(); // Additional notes
            
            // Status tracking
            $table->enum('status', [
                'pending',      // Awaiting approval
                'approved',     // Approved, ready to process
                'processing',   // Being processed with payment provider
                'completed',    // Successfully refunded
                'failed',       // Failed to process
                'rejected',     // Rejected by hotel
                'cancelled'     // Cancelled by requester
            ])->default('pending');
            
            // Payment provider details
            $table->string('payment_method'); // paymongo, paypal, cash
            $table->string('provider_refund_id')->nullable(); // ID from payment provider
            $table->json('provider_response')->nullable(); // Store API response
            $table->timestamp('processed_at')->nullable(); // When refund was processed
            
            // User tracking
            $table->string('requested_by'); // Email of user who requested refund
            $table->string('approved_by')->nullable(); // Email of user who approved
            $table->string('processed_by')->nullable(); // Email of user who processed
            $table->string('rejected_by')->nullable(); // Email of user who rejected
            
            // Timestamps for different stages
            $table->timestamp('requested_at')->default(now());
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['hotel_id', 'status']);
            $table->index(['payment_id']);
            $table->index(['invoice_id']);
            $table->index('refund_number');
        });
        
        // Create refund policy settings table
        Schema::create('refund_policies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hotel_id')->constrained()->onDelete('cascade');
            
            // Policy settings
            $table->boolean('refunds_enabled')->default(true);
            $table->integer('refund_time_limit_days')->default(30); // Days after payment
            $table->boolean('require_approval')->default(true);
            $table->decimal('auto_approve_limit', 10, 2)->default(0); // Auto approve under this amount
            $table->boolean('allow_partial_refunds')->default(true);
            
            // Refund reasons (JSON array)
            $table->json('allowed_reasons')->default(json_encode([
                'Guest dissatisfaction',
                'Service issues',
                'Booking error',
                'Duplicate payment',
                'Cancellation',
                'Other'
            ]));
            
            // Notification settings
            $table->boolean('notify_guest')->default(true);
            $table->boolean('notify_admin')->default(true);
            $table->string('notification_email')->nullable(); // Additional email for notifications
            
            $table->timestamps();
            
            $table->unique('hotel_id'); // One policy per hotel
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('refund_policies');
        Schema::dropIfExists('refunds');
    }
};
