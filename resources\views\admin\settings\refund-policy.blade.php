@include('layout.admin-header')

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Refund Policy Settings</h6>
                            <div>
                                <button type="button" class="btn btn-warning btn-sm" onclick="resetPolicy()">
                                    <i class="fas fa-undo"></i> Reset to Defaults
                                </button>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <form method="POST" action="{{ route('admin.settings.refund-policy.update') }}">
                                @csrf
                                
                                <!-- Basic Settings -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h5 class="mb-3">Basic Settings</h5>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="refunds_enabled" name="refunds_enabled" value="1"
                                                       {{ $policy->refunds_enabled ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="refunds_enabled">
                                                    Enable Refunds
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                Allow guests and staff to request refunds for payments
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="refund_time_limit_days">Refund Time Limit (Days)</label>
                                            <input type="number" name="refund_time_limit_days" id="refund_time_limit_days" 
                                                   class="form-control" min="1" max="365" 
                                                   value="{{ $policy->refund_time_limit_days }}" required>
                                            <small class="form-text text-muted">
                                                Number of days after payment when refunds are allowed
                                            </small>
                                            @error('refund_time_limit_days')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Approval Settings -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h5 class="mb-3">Approval Settings</h5>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="require_approval" name="require_approval" value="1"
                                                       {{ $policy->require_approval ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="require_approval">
                                                    Require Approval
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                Require manual approval for all refund requests
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="auto_approve_limit">Auto-Approve Limit</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">₱</span>
                                                </div>
                                                <input type="number" name="auto_approve_limit" id="auto_approve_limit" 
                                                       class="form-control" step="0.01" min="0" 
                                                       value="{{ $policy->auto_approve_limit }}">
                                            </div>
                                            <small class="form-text text-muted">
                                                Automatically approve refunds under this amount (0 = no auto-approval)
                                            </small>
                                            @error('auto_approve_limit')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Refund Options -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h5 class="mb-3">Refund Options</h5>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="allow_partial_refunds" name="allow_partial_refunds" value="1"
                                                       {{ $policy->allow_partial_refunds ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="allow_partial_refunds">
                                                    Allow Partial Refunds
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                Allow refunding only part of the payment amount
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Allowed Reasons -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h5 class="mb-3">Allowed Refund Reasons</h5>
                                        <div id="reasonsList">
                                            @foreach($policy->getDefaultReasons() as $index => $reason)
                                                <div class="input-group mb-2 reason-item">
                                                    <input type="text" name="allowed_reasons[]" class="form-control" 
                                                           value="{{ $reason }}" required>
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-danger btn-sm" 
                                                                onclick="removeReason(this)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="addReason()">
                                            <i class="fas fa-plus"></i> Add Reason
                                        </button>
                                    </div>
                                </div>

                                <!-- Notification Settings -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h5 class="mb-3">Notification Settings</h5>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="notify_guest" name="notify_guest" value="1"
                                                       {{ $policy->notify_guest ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="notify_guest">
                                                    Notify Guest
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                Send email notifications to guests about refund status
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" 
                                                       id="notify_admin" name="notify_admin" value="1"
                                                       {{ $policy->notify_admin ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="notify_admin">
                                                    Notify Admin
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">
                                                Send email notifications to hotel admins
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="notification_email">Additional Notification Email</label>
                                            <input type="email" name="notification_email" id="notification_email" 
                                                   class="form-control" value="{{ $policy->notification_email }}"
                                                   placeholder="Optional additional email">
                                            <small class="form-text text-muted">
                                                Optional email address for refund notifications
                                            </small>
                                            @error('notification_email')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Save Refund Policy
                                    </button>
                                    <a href="{{ route('admin.settings.general') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Settings
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    @include('layout.admin-logout-modal')

    @include('layout.admin-scripts')

    <script>
        function addReason() {
            const reasonsList = document.getElementById('reasonsList');
            const newReason = document.createElement('div');
            newReason.className = 'input-group mb-2 reason-item';
            newReason.innerHTML = `
                <input type="text" name="allowed_reasons[]" class="form-control" 
                       placeholder="Enter refund reason" required>
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeReason(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            reasonsList.appendChild(newReason);
        }

        function removeReason(button) {
            const reasonItems = document.querySelectorAll('.reason-item');
            if (reasonItems.length > 1) {
                button.closest('.reason-item').remove();
            } else {
                alert('At least one refund reason is required.');
            }
        }

        function resetPolicy() {
            if (confirm('Are you sure you want to reset the refund policy to default settings? This will overwrite all current settings.')) {
                fetch('{{ route("admin.settings.refund-policy.reset") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Failed to reset policy. Please try again.');
                    }
                })
                .catch(error => {
                    alert('An error occurred. Please try again.');
                });
            }
        }

        // Toggle auto-approve limit based on require approval setting
        document.getElementById('require_approval').addEventListener('change', function() {
            const autoApproveLimit = document.getElementById('auto_approve_limit');
            if (!this.checked) {
                autoApproveLimit.value = '0';
                autoApproveLimit.disabled = true;
            } else {
                autoApproveLimit.disabled = false;
            }
        });

        // Initialize the auto-approve limit state
        document.addEventListener('DOMContentLoaded', function() {
            const requireApproval = document.getElementById('require_approval');
            const autoApproveLimit = document.getElementById('auto_approve_limit');
            
            if (!requireApproval.checked) {
                autoApproveLimit.disabled = true;
            }
        });
    </script>

</body>

</html>
