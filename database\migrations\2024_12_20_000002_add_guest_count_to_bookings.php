<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->integer('guest_count')->default(1)->after('guest_phone');
            $table->integer('extra_guests')->default(0)->after('guest_count');
            $table->decimal('extra_guest_charge', 10, 2)->default(0)->after('extra_guests');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn(['guest_count', 'extra_guests', 'extra_guest_charge']);
        });
    }
};
