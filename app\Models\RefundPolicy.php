<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class RefundPolicy extends Model
{
    use HasFactory, BelongsToHotel;

    protected $fillable = [
        'hotel_id',
        'refunds_enabled',
        'refund_time_limit_days',
        'require_approval',
        'auto_approve_limit',
        'allow_partial_refunds',
        'allowed_reasons',
        'notify_guest',
        'notify_admin',
        'notification_email'
    ];

    protected $casts = [
        'refunds_enabled' => 'boolean',
        'require_approval' => 'boolean',
        'auto_approve_limit' => 'decimal:2',
        'allow_partial_refunds' => 'boolean',
        'allowed_reasons' => 'array',
        'notify_guest' => 'boolean',
        'notify_admin' => 'boolean',
    ];

    // Relationships
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    public function refunds()
    {
        return $this->hasMany(Refund::class, 'hotel_id', 'hotel_id');
    }

    // Helper methods
    public function isRefundAllowed()
    {
        return $this->refunds_enabled;
    }

    public function getDefaultReasons()
    {
        return $this->allowed_reasons ?? [
            'Guest dissatisfaction',
            'Service issues',
            'Booking error',
            'Duplicate payment',
            'Cancellation',
            'Other'
        ];
    }

    public function shouldAutoApprove($amount)
    {
        return !$this->require_approval || $amount <= $this->auto_approve_limit;
    }

    // Create default policy for hotel
    public static function createDefaultForHotel($hotelId)
    {
        return static::create([
            'hotel_id' => $hotelId,
            'refunds_enabled' => true,
            'refund_time_limit_days' => 30,
            'require_approval' => true,
            'auto_approve_limit' => 0,
            'allow_partial_refunds' => true,
            'allowed_reasons' => [
                'Guest dissatisfaction',
                'Service issues',
                'Booking error',
                'Duplicate payment',
                'Cancellation',
                'Other'
            ],
            'notify_guest' => true,
            'notify_admin' => true,
        ]);
    }
}
