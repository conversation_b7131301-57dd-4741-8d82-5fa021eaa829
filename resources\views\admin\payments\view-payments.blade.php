@include('layout.admin-header')
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Remittable Payments</h6>
                            <div class="row">
                                <div class="form-group col-md-3">
                                    <select class="form-control" id="to_user_email" name="to_user_email">
                                        <option value="">--Select Remittance Option--</option>
                                        @foreach ($users as $user)
                                            <option value="{{ $user->email }}">{{ $user->name }} -
                                                {{ $user->email }}</option>
                                        @endforeach
                                        <option value="bank">Bank Direct</option>
                                    </select>
                                </div>

                                <div class="form-group col-md-3">
                                    <button id="remit-button" class="btn btn-primary">Remit Selected Payments</button>
                                </div>
                            </div>
                            <div class="card-body">

                                {{-- Table for Pending Remittance To Confirm  --}}
                                <div class="table-responsive">



                                    <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                        cellspacing="0">
                                        <thead>

                                            <tr>
                                                <th>Select</th>
                                                <th>ID</th>
                                                <th>Guest</th>
                                                <th>Room</th>
                                                <th>Payment Method</th>
                                                <th>Amount</th>
                                                <th>Payment Date</th>
                                                <th>Created By</th>
                                                <th>Is Remitted?</th>
                                                <th>Current Location</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($remittable_payments as $remittable_payments)
                                                <tr>
                                                    @if ($remittable_payments->is_remitted == 'no')
                                                        <td><input type="checkbox" class="payment-checkbox"
                                                                value="{{ $remittable_payments->id }}"></td>
                                                    @else
                                                        <td><input type="checkbox" class="payment-checkbox"
                                                                value="{{ $remittable_payments->id }}" disabled></td>
                                                    @endif
                                                    <td>{{ $remittable_payments->id }}</td>
                                                    @if ($remittable_payments->invoice && $remittable_payments->invoice->booking && $remittable_payments->invoice->booking->guest_first_name && $remittable_payments->invoice->booking->guest_last_name)
                                                        <td>{{ $remittable_payments->invoice->booking->guest_first_name . ' ' . $remittable_payments->invoice->booking->guest_last_name }}</td>
                                                    @else
                                                        <td>N/A</td>
                                                    @endif

                                                    @if ($remittable_payments->invoice && $remittable_payments->invoice->booking && $remittable_payments->invoice->booking->room && $remittable_payments->invoice->booking->room->room_number && $remittable_payments->invoice->booking->room->roomType && $remittable_payments->invoice->booking->room->roomType->name)
                                                        <td>{{ $remittable_payments->invoice->booking->room->room_number . '-' . $remittable_payments->invoice->booking->room->roomType->name }}</td>
                                                    @else
                                                        <td>N/A</td>
                                                    @endif
                                                    <td>{{ $remittable_payments->payment_method }}</td>
                                                    <td style="text-align:right;">{{ $currencySymbol . number_format($remittable_payments->amount,2) }}</td>
                                                    <td class="checkin-date"
                                                        data-date="{{ $remittable_payments->payment_date }}">
                                                    </td>
                                                    <td>{{ $remittable_payments->created_by }}</td>
                                                    <td>{{ $remittable_payments->is_remitted }}</td>
                                                    <td>{{ $remittable_payments->current_location }}</td>
                                                    <td>
                                                        @if(auth()->user()->hasPermission('payments.refund') && $remittable_payments->canBeRefunded())
                                                            <button class="btn btn-sm btn-warning"
                                                                    onclick="checkRefundEligibility({{ $remittable_payments->id }})"
                                                                    title="Request Refund">
                                                                <i class="fas fa-undo"></i>
                                                            </button>
                                                        @endif

                                                        @if($remittable_payments->hasRefunds())
                                                            <span class="badge badge-info" title="Has refunds">
                                                                {{ ucfirst($remittable_payments->refundStatus) }} Refund
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>

                                    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
                                    <script>
                                        document.addEventListener('DOMContentLoaded', function() {
                                            $('#remit-button').on('click', function() {
                                                let selectedPayments = [];
                                                let from_user_email =
                                                '{{ get_user_email() }}'; // Ensure this function outputs the correct email format

                                                // Get selected payments
                                                $('.payment-checkbox:checked').each(function() {
                                                    selectedPayments.push($(this).val());
                                                });

                                                // Get the email of the user to remit to from the dropdown
                                                let to_user_email = $('#to_user_email').val();

                                                // Initialize totalAmount
                                                let totalAmount = 0;

                                                // Get the amount of all selected payments
                                                $('.payment-checkbox:checked').each(function() {
                                                    let amountText = $(this).closest('tr').find('td:eq(5)').text().trim();
                                                    // Use regular expression to remove all non-numeric characters except the decimal point
                                                    let amount = parseFloat(amountText.replace(/[^0-9.-]+/g, ''));

                                                    // Only add valid numbers
                                                    if (!isNaN(amount)) {
                                                        totalAmount += amount;
                                                    }
                                                });

                                                if (selectedPayments.length === 0) {
                                                    alert('Please select at least one payment to remit.');
                                                    return;
                                                }

                                                // Make the AJAX request
                                                $.ajax({
                                                    url: '{{ route('admin.remittances.store') }}',
                                                    method: 'POST',
                                                    data: {
                                                        _token: '{{ csrf_token() }}',
                                                        payments: selectedPayments,
                                                        from_user_email: from_user_email, // Include the current user's email
                                                        to_user_email: to_user_email,
                                                        amount: totalAmount // Pass the correct total amount
                                                    },
                                                    success: function(response) {
                                                        if (response.success) {
                                                            alert('Payments successfully remitted.');
                                                            location.reload();
                                                        } else {
                                                            alert('Failed to remit payments.');
                                                        }
                                                    },
                                                    error: function(xhr) {
                                                        let errorMessage = 'An error occurred: ' + xhr.statusText;
                                                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                                                            errorMessage = 'Validation Errors: ' + Object.values(xhr
                                                                .responseJSON.errors).flat().join(', ');
                                                        }
                                                        alert(errorMessage);
                                                    }
                                                });
                                            });
                                        });
                                    </script>

                                    {{-- Date Formatter --}}
                                    <script>
                                        document.addEventListener('DOMContentLoaded', function() {
                                            // Function to format dates using moment.js
                                            function formatDate(dateString) {
                                                return moment(dateString).format('MMMM D, YYYY h:mm A');
                                            }

                                            // Format all check-in dates
                                            document.querySelectorAll('.checkin-date').forEach(function(element) {
                                                const date = element.getAttribute('data-date');
                                                element.textContent = formatDate(date);
                                            });

                                            // Format all check-out dates
                                            document.querySelectorAll('.checkout-date').forEach(function(element) {
                                                const date = element.getAttribute('data-date');
                                                element.textContent = formatDate(date);
                                            });
                                        });
                                    </script>

                                    {{-- Refund Eligibility Check --}}
                                    <script>
                                        // Refund eligibility check function
                                        window.checkRefundEligibility = function(paymentId) {
                                            fetch(`/admin/payments/${paymentId}/refund-eligibility`)
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.eligible) {
                                                        if (confirm('This payment is eligible for refund. Would you like to create a refund request?')) {
                                                            window.location.href = `/admin/refunds/create?payment_id=${paymentId}`;
                                                        }
                                                    } else {
                                                        let message = 'This payment is not eligible for refund:\n\n';
                                                        data.reasons.forEach(reason => {
                                                            message += '• ' + reason + '\n';
                                                        });
                                                        alert(message);
                                                    }
                                                })
                                                .catch(error => {
                                                    console.error('Error checking refund eligibility:', error);
                                                    alert('Failed to check refund eligibility. Please try again.');
                                                });
                                        };
                                    </script>

                                </div>
                            </div>
                        </div>


                    </div>

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">All Payments</h6>

                            <div class="card-body">
                                {{-- Table for All Payments  --}}
                                <div class="table-responsive">



                                    <table id="myRequests2" class="table table-bordered table-striped" width="100%"
                                        cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Guest</th>
                                                <th>Room</th>
                                                <th>Payment Method</th>
                                                <th>Amount</th>
                                                <th>Payment Date</th>
                                                <th>Created By</th>
                                                <th>Is Remitted?</th>
                                                <th>Current Location</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($all_payments as $all_payment)
                                                <tr>

                                                    <td>{{ $all_payment->id }}</td>
                                                    @if ($all_payment->invoice && $all_payment->invoice->booking && $all_payment->invoice->booking->guest_first_name && $all_payment->invoice->booking->guest_last_name)
                                                        <td>{{ $all_payment->invoice->booking->guest_first_name . ' ' . $all_payment->invoice->booking->guest_last_name }}</td>
                                                    @else
                                                        <td>N/A</td>
                                                    @endif

                                                    @if ($all_payment->invoice && $all_payment->invoice->booking && $all_payment->invoice->booking->room && $all_payment->invoice->booking->room->room_number && $all_payment->invoice->booking->room->roomType && $all_payment->invoice->booking->room->roomType->name)
                                                        <td>{{ $all_payment->invoice->booking->room->room_number . '-' . $all_payment->invoice->booking->room->roomType->name }}</td>
                                                    @else
                                                        <td>N/A</td>
                                                    @endif
                                                    <td>{{ $all_payment->payment_method }}</td>
                                                    <td style="text-align:right;">{{ $currencySymbol . $all_payment->amount }}</td>
                                                    <td class="checkin-date"
                                                        data-date="{{ $all_payment->payment_date }}">
                                                    </td>
                                                    <td>{{ $all_payment->created_by }}</td>
                                                    <td>{{ $all_payment->is_remitted }}</td>
                                                    <td>{{ $all_payment->current_location }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    <script src="{{ asset('assets/js/moment.min.js') }}"></script>



                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    @include('layout.scripts')

</body>

</html>
