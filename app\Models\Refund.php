<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;
use Carbon\Carbon;

class Refund extends Model
{
    use HasFactory, BelongsToHotel;

    protected $fillable = [
        'hotel_id',
        'payment_id',
        'invoice_id',
        'refund_number',
        'refund_amount',
        'original_amount',
        'refund_type',
        'reason',
        'notes',
        'status',
        'payment_method',
        'provider_refund_id',
        'provider_response',
        'processed_at',
        'requested_by',
        'approved_by',
        'processed_by',
        'rejected_by',
        'requested_at',
        'approved_at',
        'rejected_at'
    ];

    protected $casts = [
        'refund_amount' => 'decimal:2',
        'original_amount' => 'decimal:2',
        'provider_response' => 'array',
        'processed_at' => 'datetime',
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    // Relationships
    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    // User relationships
    public function requester()
    {
        return $this->belongsTo(User::class, 'requested_by', 'email');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by', 'email');
    }

    public function processor()
    {
        return $this->belongsTo(User::class, 'processed_by', 'email');
    }

    public function rejector()
    {
        return $this->belongsTo(User::class, 'rejected_by', 'email');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    // Helper methods
    public function canBeApproved()
    {
        return $this->status === 'pending';
    }

    public function canBeProcessed()
    {
        return $this->status === 'approved';
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['pending', 'approved']);
    }

    public function canBeRejected()
    {
        return $this->status === 'pending';
    }

    public function isPartialRefund()
    {
        return $this->refund_type === 'partial';
    }

    public function isFullRefund()
    {
        return $this->refund_type === 'full';
    }

    public function getRefundPercentageAttribute()
    {
        if ($this->original_amount == 0) {
            return 0;
        }
        return round(($this->refund_amount / $this->original_amount) * 100, 2);
    }

    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'pending' => 'badge-warning',
            'approved' => 'badge-info',
            'processing' => 'badge-primary',
            'completed' => 'badge-success',
            'failed' => 'badge-danger',
            'rejected' => 'badge-danger',
            'cancelled' => 'badge-secondary',
            default => 'badge-secondary'
        };
    }

    public function getFormattedStatusAttribute()
    {
        return ucfirst(str_replace('_', ' ', $this->status));
    }

    // Generate unique refund number
    public static function generateRefundNumber($hotelId)
    {
        $date = now()->format('Ymd');
        $prefix = "REF-{$date}-";
        
        // Get the last refund number for today
        $lastRefund = static::where('hotel_id', $hotelId)
            ->where('refund_number', 'like', $prefix . '%')
            ->orderBy('refund_number', 'desc')
            ->first();

        if ($lastRefund) {
            $lastNumber = (int) substr($lastRefund->refund_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Check if refund is within time limit
    public function isWithinTimeLimit()
    {
        $policy = RefundPolicy::where('hotel_id', $this->hotel_id)->first();
        if (!$policy || !$policy->refunds_enabled) {
            return false;
        }

        $timeLimitDays = $policy->refund_time_limit_days;
        $paymentDate = $this->payment->payment_date;
        
        return $paymentDate->diffInDays(now()) <= $timeLimitDays;
    }

    // Check if refund should be auto-approved
    public function shouldAutoApprove()
    {
        $policy = RefundPolicy::where('hotel_id', $this->hotel_id)->first();
        if (!$policy || $policy->require_approval) {
            return false;
        }

        return $this->refund_amount <= $policy->auto_approve_limit;
    }
}
