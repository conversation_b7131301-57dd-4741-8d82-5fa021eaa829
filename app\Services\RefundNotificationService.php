<?php

namespace App\Services;

use App\Models\Refund;
use App\Models\RefundPolicy;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class RefundNotificationService
{
    /**
     * Send refund status notification
     */
    public function sendRefundStatusNotification(Refund $refund, string $status, array $additionalData = [])
    {
        try {
            $policy = RefundPolicy::where('hotel_id', $refund->hotel_id)->first();
            
            if (!$policy) {
                return;
            }

            // Prepare notification data
            $data = [
                'refund' => $refund,
                'status' => $status,
                'hotel' => $refund->hotel,
                'guest_name' => $this->getGuestName($refund),
                'guest_email' => $this->getGuestEmail($refund),
                'additional_data' => $additionalData
            ];

            // Send guest notification
            if ($policy->notify_guest && $data['guest_email']) {
                $this->sendGuestNotification($data);
            }

            // Send admin notification
            if ($policy->notify_admin) {
                $this->sendAdminNotification($data);
            }

            // Send additional notification email
            if ($policy->notification_email) {
                $this->sendAdditionalNotification($data, $policy->notification_email);
            }

        } catch (\Exception $e) {
            Log::error('Failed to send refund notification', [
                'refund_id' => $refund->id,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send notification when refund is requested
     */
    public function sendRefundRequestedNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'requested');
    }

    /**
     * Send notification when refund is approved
     */
    public function sendRefundApprovedNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'approved');
    }

    /**
     * Send notification when refund is rejected
     */
    public function sendRefundRejectedNotification(Refund $refund, string $reason)
    {
        $this->sendRefundStatusNotification($refund, 'rejected', ['rejection_reason' => $reason]);
    }

    /**
     * Send notification when refund is processed
     */
    public function sendRefundProcessedNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'processed');
    }

    /**
     * Send notification when refund is completed
     */
    public function sendRefundCompletedNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'completed');
    }

    /**
     * Send notification when refund fails
     */
    public function sendRefundFailedNotification(Refund $refund, string $error)
    {
        $this->sendRefundStatusNotification($refund, 'failed', ['error_message' => $error]);
    }

    /**
     * Send notification when refund is cancelled
     */
    public function sendRefundCancelledNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'cancelled');
    }

    /**
     * Send guest notification
     */
    private function sendGuestNotification(array $data)
    {
        if (!$data['guest_email']) {
            return;
        }

        $subject = $this->getGuestEmailSubject($data['status'], $data['refund']);
        $message = $this->getGuestEmailMessage($data);

        // For now, we'll use a simple mail approach
        // In a real application, you might want to use Laravel's Mail facade with proper templates
        $this->sendSimpleEmail($data['guest_email'], $subject, $message);
    }

    /**
     * Send admin notification
     */
    private function sendAdminNotification(array $data)
    {
        // Get hotel admin emails
        $adminEmails = $this->getHotelAdminEmails($data['refund']->hotel_id);
        
        if (empty($adminEmails)) {
            return;
        }

        $subject = $this->getAdminEmailSubject($data['status'], $data['refund']);
        $message = $this->getAdminEmailMessage($data);

        foreach ($adminEmails as $email) {
            $this->sendSimpleEmail($email, $subject, $message);
        }
    }

    /**
     * Send additional notification
     */
    private function sendAdditionalNotification(array $data, string $email)
    {
        $subject = $this->getAdminEmailSubject($data['status'], $data['refund']);
        $message = $this->getAdminEmailMessage($data);

        $this->sendSimpleEmail($email, $subject, $message);
    }

    /**
     * Get guest name from refund
     */
    private function getGuestName(Refund $refund): string
    {
        if ($refund->invoice && $refund->invoice->booking) {
            $booking = $refund->invoice->booking;
            return trim($booking->guest_first_name . ' ' . $booking->guest_last_name);
        }
        
        return 'Guest';
    }

    /**
     * Get guest email from refund
     */
    private function getGuestEmail(Refund $refund): ?string
    {
        if ($refund->invoice && $refund->invoice->booking) {
            return $refund->invoice->booking->guest_email;
        }
        
        return null;
    }

    /**
     * Get hotel admin emails
     */
    private function getHotelAdminEmails(int $hotelId): array
    {
        // This would typically query the users table for hotel admins
        // For now, we'll return an empty array as this depends on your user/role system
        return [];
    }

    /**
     * Get guest email subject
     */
    private function getGuestEmailSubject(string $status, Refund $refund): string
    {
        $hotelName = $refund->hotel->name ?? 'Hotel';
        
        switch ($status) {
            case 'requested':
                return "Refund Request Received - {$refund->refund_number}";
            case 'approved':
                return "Refund Approved - {$refund->refund_number}";
            case 'rejected':
                return "Refund Request Declined - {$refund->refund_number}";
            case 'processed':
                return "Refund Being Processed - {$refund->refund_number}";
            case 'completed':
                return "Refund Completed - {$refund->refund_number}";
            case 'failed':
                return "Refund Processing Failed - {$refund->refund_number}";
            case 'cancelled':
                return "Refund Cancelled - {$refund->refund_number}";
            default:
                return "Refund Update - {$refund->refund_number}";
        }
    }

    /**
     * Get admin email subject
     */
    private function getAdminEmailSubject(string $status, Refund $refund): string
    {
        switch ($status) {
            case 'requested':
                return "New Refund Request - {$refund->refund_number}";
            case 'approved':
                return "Refund Approved - {$refund->refund_number}";
            case 'rejected':
                return "Refund Rejected - {$refund->refund_number}";
            case 'processed':
                return "Refund Processed - {$refund->refund_number}";
            case 'completed':
                return "Refund Completed - {$refund->refund_number}";
            case 'failed':
                return "Refund Failed - {$refund->refund_number}";
            case 'cancelled':
                return "Refund Cancelled - {$refund->refund_number}";
            default:
                return "Refund Update - {$refund->refund_number}";
        }
    }

    /**
     * Get guest email message
     */
    private function getGuestEmailMessage(array $data): string
    {
        $refund = $data['refund'];
        $status = $data['status'];
        $hotelName = $refund->hotel->name ?? 'Hotel';
        $currencySymbol = config('currency.symbol', '$');
        
        $message = "Dear {$data['guest_name']},\n\n";
        
        switch ($status) {
            case 'requested':
                $message .= "We have received your refund request for {$currencySymbol}" . number_format($refund->refund_amount, 2) . ".\n\n";
                $message .= "Refund Number: {$refund->refund_number}\n";
                $message .= "Reason: {$refund->reason}\n\n";
                $message .= "We will review your request and notify you of the outcome.\n";
                break;
                
            case 'approved':
                $message .= "Your refund request has been approved!\n\n";
                $message .= "Refund Number: {$refund->refund_number}\n";
                $message .= "Amount: {$currencySymbol}" . number_format($refund->refund_amount, 2) . "\n\n";
                $message .= "Your refund will be processed shortly.\n";
                break;
                
            case 'rejected':
                $message .= "Unfortunately, your refund request has been declined.\n\n";
                $message .= "Refund Number: {$refund->refund_number}\n";
                if (isset($data['additional_data']['rejection_reason'])) {
                    $message .= "Reason: {$data['additional_data']['rejection_reason']}\n";
                }
                $message .= "\nIf you have any questions, please contact us.\n";
                break;
                
            case 'processed':
                $message .= "Your refund is being processed.\n\n";
                $message .= "Refund Number: {$refund->refund_number}\n";
                $message .= "Amount: {$currencySymbol}" . number_format($refund->refund_amount, 2) . "\n\n";
                $message .= "You should receive the refund in your original payment method within 3-5 business days.\n";
                break;
                
            case 'completed':
                $message .= "Your refund has been completed!\n\n";
                $message .= "Refund Number: {$refund->refund_number}\n";
                $message .= "Amount: {$currencySymbol}" . number_format($refund->refund_amount, 2) . "\n\n";
                $message .= "The refund has been sent to your original payment method.\n";
                break;
                
            case 'failed':
                $message .= "There was an issue processing your refund.\n\n";
                $message .= "Refund Number: {$refund->refund_number}\n";
                $message .= "We are working to resolve this issue. Please contact us for assistance.\n";
                break;
                
            case 'cancelled':
                $message .= "Your refund request has been cancelled.\n\n";
                $message .= "Refund Number: {$refund->refund_number}\n";
                break;
        }
        
        $message .= "\nThank you for choosing {$hotelName}.\n\n";
        $message .= "Best regards,\n{$hotelName} Team";
        
        return $message;
    }

    /**
     * Get admin email message
     */
    private function getAdminEmailMessage(array $data): string
    {
        $refund = $data['refund'];
        $status = $data['status'];
        $currencySymbol = config('currency.symbol', '$');
        
        $message = "Refund Status Update\n\n";
        $message .= "Refund Number: {$refund->refund_number}\n";
        $message .= "Guest: {$data['guest_name']}\n";
        $message .= "Amount: {$currencySymbol}" . number_format($refund->refund_amount, 2) . "\n";
        $message .= "Payment Method: " . ucfirst($refund->payment_method) . "\n";
        $message .= "Status: " . ucfirst($status) . "\n";
        $message .= "Reason: {$refund->reason}\n\n";
        
        if (isset($data['additional_data']['rejection_reason'])) {
            $message .= "Rejection Reason: {$data['additional_data']['rejection_reason']}\n\n";
        }
        
        if (isset($data['additional_data']['error_message'])) {
            $message .= "Error: {$data['additional_data']['error_message']}\n\n";
        }
        
        $message .= "View refund details: " . route('admin.refunds.show', $refund) . "\n";
        
        return $message;
    }

    /**
     * Send simple email (placeholder implementation)
     */
    private function sendSimpleEmail(string $to, string $subject, string $message)
    {
        // This is a placeholder implementation
        // In a real application, you would use Laravel's Mail facade
        // with proper email templates and configuration
        
        Log::info('Refund notification email', [
            'to' => $to,
            'subject' => $subject,
            'message' => $message
        ]);
        
        // Uncomment the following lines when you have proper email configuration:
        /*
        try {
            Mail::raw($message, function ($mail) use ($to, $subject) {
                $mail->to($to)->subject($subject);
            });
        } catch (\Exception $e) {
            Log::error('Failed to send refund notification email', [
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);
        }
        */
    }
}
