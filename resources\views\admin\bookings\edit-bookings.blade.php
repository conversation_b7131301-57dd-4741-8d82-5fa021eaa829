@include('layout.admin-header')

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Edit Booking</h6>
                            
                        </div>
                        <div class="card-body">
                            <!-- Form to Edit Booking -->
                            <!-- Include Select2 CSS -->
                            <link href="{{ asset('assets/css/select2.min.css') }}" rel="stylesheet" />
                            <!-- Include Select2 JS -->
                            <script src="{{ asset('assets/js/select2.min.js') }}" defer></script>
                            {{-- Form to Create Booking --}}
                            <form action="{{ route('admin.booking.update', $booking->id) }}" method="POST">
                                @csrf
                                @method('PATCH')
                                <div class="row">
                                    <div class="form-group col-md-6">
                                        <label for="guest_info">Select Guest</label>
                                        <select class="form-control select2-guest" id="guest_info" name="guest_info"
                                            style="width: 100%;">
                                            <option value="">-- Select Guest --</option>
                                            <!-- Options will be dynamically added here -->
                                        </select>
                                    </div>
                                    
                                    <div class="form-group col-md-3">
                                        <label for="checkin_date">Check-In Date</label>
                                        <input type="text" class="form-control" id="checkin_date" name="checkin_date"
                                            value="{{ old('checkin_date', \Carbon\Carbon::parse($booking->checkin_date)->format('Y-m-d H:i')) }}"
                                            required autocomplete="off">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="checkout_date">Check-Out Date <span id="no_of_days" class="bg-warning small" style="border-radius:3px;padding:3px;">1 day</span></span></label>
                                        <input type="text" class="form-control" id="checkout_date"
                                            name="checkout_date"
                                            value="{{ old('checkout_date', \Carbon\Carbon::parse($booking->checkout_date)->format('Y-m-d H:i')) }}"
                                            required autocomplete="off">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="guest_first_name">First Name</label>
                                        <input class="form-control" id="guest_first_name" name="guest_first_name"
                                            value="{{ old('guest_first_name', $booking->guest_first_name) }}"
                                            placeholder="*" required>
                                        <input type="hidden" id="guest_id" name="guest_id" 
                                            value="{{ old('guest_id', $booking->guest_id) }}">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="guest_last_name">Last Name</label>
                                        <input class="form-control" id="guest_last_name" name="guest_last_name"
                                            value="{{ old('guest_last_name', $booking->guest_last_name) }}"
                                            placeholder="*" required>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="room_id">Room<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span></label>
                                        <select class="form-control select2-room" id="room_id" name="room_id">
                                            <option value="">Select Room</option>
                                            @foreach ($rooms as $room)
                                                <option value="{{ $room->id }}"
                                                    data-price="{{ $room->individual_room_rate }}"
                                                    {{ $room->id == old('room_id', $booking->room_id) ? 'selected' : '' }}>
                                                    {{ $room->room_number }} - {{ $room->roomType->name }} ({{ $room->individual_room_rate }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="guest_phone">Phone</label>
                                        <input class="form-control" id="guest_phone" name="guest_phone"
                                            autocomplete="off" value="{{ old('guest_phone', $booking->guest_phone) }}"
                                            placeholder="*" required>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="guest_email">Email</label>
                                        <input class="form-control" id="guest_email" name="guest_email"
                                            value="{{ old('guest_email', $booking->guest_email) }}"
                                            title="{{ old('guest_email', $booking->guest_email) }}"
                                            placeholder="---">
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="guest_count">Number of Guests</label>
                                        <select class="form-control" id="guest_count" name="guest_count" required>
                                            <option value="">Select Guest Count</option>
                                            @for($i = 1; $i <= 10; $i++)
                                                <option value="{{ $i }}" {{ old('guest_count', $booking->guest_count ?? 1) == $i ? 'selected' : '' }}>
                                                    {{ $i }} Guest{{ $i > 1 ? 's' : '' }}
                                                </option>
                                            @endfor
                                        </select>
                                        <small class="form-text text-muted">
                                            Extra charges may apply for guests exceeding room capacity
                                            @if($booking->extra_guests > 0)
                                                <br><strong>Current extra guests: {{ $booking->extra_guests }} ({{ number_format($booking->extra_guest_charge, 2) }} charge)</strong>
                                            @endif
                                        </small>
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="room_price">Room Rate</label>
                                        <input type="text" id="room_price" name="room_price" class="form-control text-right"
                                            value="{{ old('room_price', $booking->room_price) }}" readonly>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="total_price_visible">Sub-Total</label>
                                        <input type="text" id="total_price_visible" name="total_price_visible" class="form-control text-right" style="font-size:1.2em;"
                                            value="" readonly>
                                        <input type="hidden" id="total_price" name="total_price" class="form-control text-right"
                                            value="" readonly><!--vince added-->
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="notes">Add a Note</label>
                                        <textarea class="form-control" id="notes" name="notes"
                                            placeholder="---"></textarea>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="status">Booking Status<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span></label>
                                        <select class="form-control" id="booking_status" name="booking_status">
                                            <option value="">--Select Status--</option>
                                            <option value="pending"
                                                {{ old('booking_status', $booking->booking_status) == 'pending' ? 'selected' : '' }}>
                                                Pending
                                            </option>
                                            <option value="booked"
                                                {{ old('booking_status', $booking->booking_status) == 'booked' ? 'selected' : '' }}>
                                                Booked
                                            </option>
                                            <option value="checked_in"
                                                {{ old('booking_status', $booking->booking_status) == 'checked_in' ? 'selected' : '' }}>
                                                Checked In
                                            </option>
                                            <option value="checked_out"
                                                {{ old('booking_status', $booking->booking_status) == 'checked_out' ? 'selected' : '' }}>
                                                Checked Out
                                            </option>
                                            <option value="cancelled"
                                                {{ old('booking_status', $booking->booking_status) == 'cancelled' ? 'selected' : '' }}>
                                                Cancelled
                                            </option>
                                        </select>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <table class="table-dark table-bordered small" style="width:100%;background:#003300;">
                                            <tr>
                                                <td>Booked:{{Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $booking->created_at)->format('m/d/y h:i A')}}</td><td>{{$booking->booked_by}}</td>
                                            </tr>
                                            <tr>
                                                <td>Checked In:</td><td>{{$booking->checked_in_by}}</td>
                                            </tr>
                                            <tr>
                                                <td>Checked Out:</td><td>{{$booking->checked_out_by}}</td>
                                            </tr>
                                            <tr>
                                                <td>Cancelled:</td><td>{{$booking->cancelled_by}}</td>
                                            </tr>
                                            <tr>
                                                <td>Last Updated:{{Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $booking->updated_at)->format('m/d/y h:i A')}}</td><td>{{$booking->last_updated_by}}</td>
                                            </tr>
                                        </table>
                                    </div>

                                    <!-- Hidden fields to be updated based on booking status -->
                                    <input type="hidden" id="booked_by" name="booked_by"
                                        value="{{ $booking->booked_by }}">
                                    <input type="hidden" id="cancelled_by" name="cancelled_by"
                                        value="{{ $booking->cancelled_by }}">
                                    <input type="hidden" id="checked_in_by" name="checked_in_by"
                                        value="{{ $booking->checked_in_by }}">
                                    <input type="hidden" id="checked_out_by" name="checked_out_by"
                                        value="{{ $booking->checked_out_by }}">

                                    <script>
                                        //script that handles booked_by, cancelled_by, checkedin/out_by
                                        document.addEventListener('DOMContentLoaded', function() {
                                            const bookingStatusSelect = document.getElementById('booking_status');
                                            const bookedByInput = document.getElementById('booked_by');
                                            const cancelledByInput = document.getElementById('cancelled_by');
                                            const checkedInByInput = document.getElementById('checked_in_by');
                                            const checkedOutByInput = document.getElementById('checked_out_by');

                                            // Authenticated user's email
                                            const userEmail = "{{ Auth::check() && Auth::user()->email ? Auth::user()->email : 'no_user' }}";

                                            bookingStatusSelect.addEventListener('change', function() {
                                                const selectedStatus = bookingStatusSelect.value;

                                                // Clear previous values
                                                bookedByInput.value = '';
                                                cancelledByInput.value = '';
                                                checkedInByInput.value = '';
                                                checkedOutByInput.value = '';

                                                // Update the corresponding input based on the selected status
                                                if (selectedStatus === 'booked') {
                                                    bookedByInput.value = userEmail;
                                                } else if (selectedStatus === 'cancelled') {
                                                    cancelledByInput.value = userEmail;
                                                } else if (selectedStatus === 'checked_in') {
                                                    checkedInByInput.value = userEmail;
                                                } else if (selectedStatus === 'checked_out') {
                                                    checkedOutByInput.value = userEmail;
                                                }
                                            });

                                            // Trigger change event on page load to set the initial values
                                            bookingStatusSelect.dispatchEvent(new Event('change'));
                                        });
                                    </script>

                                    <!-- Hidden Inputs to track who updated this booking -->
                                    <input type="hidden" name="updated_by"
                                        value="{{ Auth::check() && Auth::user()->email ? Auth::user()->email : 'no_user' }}">

                                    <div class="form-group col-md-12">
                                        <div style="margin-top:30px;text-align:center;">
                                            <button type="submit" class="btn btn-primary">Update Booking</button>
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label for="old_notes">Notes</label>
                                        <div class="bg-info card p-2" style="white-space: pre-wrap;min-height:100px;" id="old_notes">{{old('notes', $booking->notes)}}</div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <div style="margin-top:30px;text-align:center;">
                                            <a href="{{ route('admin.invoice.edit',  $booking->invoices->first()->id) }}"
                                                class="btn btn-success" title="Invoice">Edit Invoice
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>



    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('assets/js/sb-admin-2.min.js') }}"></script>

    <!-- Datetime Picker JS and CSS-->
    <script src="{{ asset('assets/js/datetimepicker.js') }}"></script>

    <script>
        //vince added block
        $(document).ready(function() {
            $('input[required]').each(function() {
                // Find the label associated with the required input
                var label = $("label[for='" + $(this).attr('id') + "']");
                
                // Append a red asterisk to the label if it doesn't already have one
                if (label.length && !label.find('.required-asterisk').length) {
                    label.append('<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span>');
                }
            });
        });
        //vince end
    </script>
    <!-- JavaScript Initializer for Date Picker -->
    <script>
        $(document).ready(function() {
            $('#checkin_date, #checkout_date').datetimepicker({
                format: 'Y-m-d H:i',
                step: 30,
                autoclose: true
            }).on('dp.change', function(e) {
                fetchAvailableRooms(); // Fetch available rooms on date change
            });

            $('#checkin_date, #checkout_date').on('change', function() {
                var checkinDate = $('#checkin_date').val();
                var checkoutDate = $('#checkout_date').val();

                if (checkinDate && checkoutDate) {
                    $.ajax({
                        url: '{{ route('get.available.rooms') }}',
                        method: 'POST',
                        data: {
                            checkin_date: checkinDate,
                            checkout_date: checkoutDate,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            $('#room_id').empty();
                            if (response.length > 0) {
                                $.each(response, function(index, room) {
                                    const roomTypeName = room.room_type ? room.room_type
                                        .name : 'Unknown Room Type';
                                    const roomNumber = room.room_number || 'Undefined';
                                    const roomRate = room.room_type ? room.room_type
                                        .rate : 0;
                                    $('#room_id').append(
                                        `<option value="${room.id}" data-price="${roomRate}">${roomNumber} - ${roomTypeName}</option>`
                                    );
                                });
                            } else {
                                $('#room_id').append(
                                    '<option value="">No rooms available</option>');
                            }
                        }
                    });
                }
            });
        });
    </script>

    <!-- Booking Form JavaScript -->
    <script>
        $(document).ready(function() {

            // Function to fetch available rooms based on the date change
            function fetchAvailableRooms() {
                var checkinDate = $('#checkin_date').val();
                var checkoutDate = $('#checkout_date').val();

                if (checkinDate && checkoutDate) {
                    $.ajax({
                        url: '{{ route('get.available.rooms') }}',
                        method: 'POST',
                        data: {
                            checkin_date: checkinDate,
                            checkout_date: checkoutDate,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            var selectedRoomId = $('#room_id')
                                .val(); // Save previously selected room id
                            $('#room_id').empty(); // Clear the room options

                            if (response.length > 0) {
                                var foundSelectedRoom = false;
                                $.each(response, function(index, room) {
                                    const roomTypeName = room.room_type ? room.room_type.name :
                                        'Unknown Room Type';
                                    const roomNumber = room.room_number || 'Undefined';
                                    const roomRate = room.room_type ? room
                                        .individual_room_rate : 0;

                                    $('#room_id').append(
                                        `<option value="${room.id}" data-price="${roomRate}" ${room.id == selectedRoomId ? 'selected' : ''}>${roomNumber} - ${roomTypeName}</option>`
                                    );

                                    if (room.id == selectedRoomId) {
                                        foundSelectedRoom = true;
                                    }
                                });

                                if (!foundSelectedRoom) {
                                    $('#room_id').append(
                                        '<option value="">No rooms available</option>');
                                }
                            } else {
                                $('#room_id').append('<option value="">No rooms available</option>');
                            }

                            // Trigger change event to update room price and total price
                            $('#room_id').trigger('change');
                        }
                    });
                }
            }

            // Function to calculate total price
            function calculateTotalPrice() {
                var totalPrice = 0;
                var selectedRoom = $('#room_id').find(':selected');
                if (selectedRoom.length > 0) {
                    // Get the room rate and calculate total price
                    const roomRate = parseFloat(selectedRoom.data('price')) || 0;
                    const checkInDate = new Date($('#checkin_date').val());
                    const checkOutDate = new Date($('#checkout_date').val());

                    // Calculate time difference in milliseconds
                    const timeDiff = checkOutDate - checkInDate;

                    // Calculate the difference in hours
                    const hoursDiff = timeDiff / (1000 * 60 * 60);

                    // Calculate the number of full days (round down)
                    let daysDiff = Math.ceil(hoursDiff / 24);//vince orig floor

                    // If more than 24 hours but less than a full additional day, add one extra day (CHANGED TO 0)
                    if (hoursDiff % 24 >= 1) {
                        daysDiff += 0;
                    }
                    //vince added block
                    var days = ' day';
                    if (daysDiff > 1){
                        days = " days";
                    }
                    $('#no_of_days').text(daysDiff + days);
                    //vince end

                    // Calculate total price based on full days
                    totalPrice = roomRate * daysDiff;

                    // Update the room rate input field and total price input field
                    $('#room_price').val(roomRate.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
                    $('#total_price').val(totalPrice.toFixed(2));
                    $('#total_price_visible').val(totalPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
                } else {
                    $('#room_price').val(''); // Clear the room rate field if no room is selected
                    $('#total_price').val(''); // Clear the total price field if no room is selected
                    $('#total_price_visible').val('');
                }
            }


            // Attach change event listeners to date inputs and fetch rooms accordingly
            /* vince $('#checkin_date, #checkout_date').on('change', function() {
                fetchAvailableRooms(); // Fetch available rooms on date change
            }); */

            // Update total price whenever the room or dates change
            $('#room_id').on('change', function() {
                calculateTotalPrice(); // Recalculate price when room changes
            });

            $('#checkin_date, #checkout_date').on('change', function() {
                calculateTotalPrice(); // Recalculate price when date changes
            });

            // Trigger change events on page load to initialize the form
            $('#room_id').trigger('change');
        });
    </script>
    <!-- Initialize Select2 and Handle Guest Information -->
    <script>
        $(document).ready(function() {
            // Initialize Select2 for room selection
            $('.select2-room').select2({
                placeholder: 'Select a Room',
                allowClear: true
            });

            // Room selection change event to recalculate price
            /* vince $('.select2-room').on('change', function() {
                calculateTotalPrice(); // Reuse your existing function
            }); */
            //vince added block
            $('#room_id').on('select2:select', function(e) {
                calculateTotalPrice();
            });
            //vince end
            $('.select2-guest').select2({
                //vince tags: true, // Allows users to add new items
                placeholder: 'Select Guest',
                allowClear: true,
                ajax: {
                    url: "{{ route('admin.ajax.getGuests') }}", // Your API endpoint
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term // Query parameter for search
                        };
                    },
                    processResults: function(data) {
                        // Transform the API response into the format required by Select2
                        return {
                            results: data.map(function(guest) {
                                return {
                                    id: guest.id,
                                    text: guest.first_name + ' ' + guest.last_name,
                                    first_name: guest.first_name,
                                    last_name: guest.last_name,
                                    email: guest.email,
                                    phone: guest.phone
                                };
                            })
                        };
                    },
                    cache: true
                }
            });

            $('#guest_info').on('select2:select', function(e) {
                var data = e.params.data;

                // Assuming data contains guest details like this
                $('#guest_first_name').val(data.first_name || '');
                $('#guest_last_name').val(data.last_name || '');
                $('#guest_email').val(data.email || '');
                $('#guest_phone').val(data.phone || '');
                $('#guest_id').val(data.id || '');//vince added
            });
        });
    </script>



</body>
