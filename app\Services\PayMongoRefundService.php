<?php

namespace App\Services;

use App\Models\Refund;
use App\Models\Payment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

class PayMongoRefundService
{
    private $baseUrl = 'https://api.paymongo.com';
    private $secretKey;

    public function __construct()
    {
        $this->secretKey = get_setting('paymongo_secret_key', env('PAYMONGO_SECRET_KEY'));
    }

    /**
     * Process a refund through PayMongo API
     */
    public function processRefund(Refund $refund)
    {
        try {
            // Validate refund can be processed
            if (!$refund->canBeProcessed()) {
                throw new Exception('Refund cannot be processed in current status: ' . $refund->status);
            }

            // Get the payment details
            $payment = $refund->payment;
            if (!$payment) {
                throw new Exception('Payment not found for refund');
            }

            // We need to get the PayMongo payment ID from the payment
            // This should be stored when the payment is made
            $paymongoPaymentId = $this->getPayMongoPaymentId($payment);
            if (!$paymongoPaymentId) {
                throw new Exception('PayMongo payment ID not found for this payment');
            }

            // Update refund status to processing
            $refund->update([
                'status' => 'processing',
                'processed_by' => auth()->user()->email ?? 'system',
                'processed_at' => now()
            ]);

            // Prepare refund data
            $refundData = [
                'data' => [
                    'attributes' => [
                        'amount' => (int)($refund->refund_amount * 100), // Convert to centavos
                        'payment_id' => $paymongoPaymentId,
                        'reason' => $this->mapRefundReason($refund->reason),
                        'notes' => $refund->notes ?? ''
                    ]
                ]
            ];

            // Make API call to PayMongo
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Basic ' . base64_encode($this->secretKey . ':')
            ])->post($this->baseUrl . '/refunds', $refundData);

            // Handle response
            if ($response->successful()) {
                $responseData = $response->json();
                
                // Update refund with success data
                $refund->update([
                    'status' => 'completed',
                    'provider_refund_id' => $responseData['data']['id'] ?? null,
                    'provider_response' => $responseData,
                    'processed_at' => now()
                ]);

                Log::info('PayMongo refund processed successfully', [
                    'refund_id' => $refund->id,
                    'refund_number' => $refund->refund_number,
                    'amount' => $refund->refund_amount,
                    'paymongo_refund_id' => $responseData['data']['id'] ?? null
                ]);

                return [
                    'success' => true,
                    'message' => 'Refund processed successfully',
                    'data' => $responseData
                ];

            } else {
                // Handle API error
                $errorData = $response->json();
                
                $refund->update([
                    'status' => 'failed',
                    'provider_response' => $errorData,
                    'processed_at' => now()
                ]);

                Log::error('PayMongo refund failed', [
                    'refund_id' => $refund->id,
                    'refund_number' => $refund->refund_number,
                    'error' => $errorData,
                    'status_code' => $response->status()
                ]);

                return [
                    'success' => false,
                    'message' => 'Refund failed: ' . ($errorData['errors'][0]['detail'] ?? 'Unknown error'),
                    'data' => $errorData
                ];
            }

        } catch (Exception $e) {
            // Update refund status to failed
            $refund->update([
                'status' => 'failed',
                'provider_response' => ['error' => $e->getMessage()],
                'processed_at' => now()
            ]);

            Log::error('PayMongo refund exception', [
                'refund_id' => $refund->id,
                'refund_number' => $refund->refund_number,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Refund failed: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Check refund status from PayMongo
     */
    public function checkRefundStatus($refundId)
    {
        try {
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Authorization' => 'Basic ' . base64_encode($this->secretKey . ':')
            ])->get($this->baseUrl . '/refunds/' . $refundId);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to check refund status',
                'data' => $response->json()
            ];

        } catch (Exception $e) {
            Log::error('PayMongo refund status check failed', [
                'refund_id' => $refundId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Error checking refund status: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get PayMongo payment ID from payment record
     * This assumes the payment ID is stored somewhere in the payment record
     */
    private function getPayMongoPaymentId(Payment $payment)
    {
        // For now, we'll need to implement a way to store and retrieve PayMongo payment IDs
        // This could be in a separate field or extracted from provider response
        
        // Check if we have stored the PayMongo payment ID
        if (isset($payment->provider_payment_id)) {
            return $payment->provider_payment_id;
        }

        // If not stored separately, we might need to extract from session or other source
        // For now, return null and handle this case
        return null;
    }

    /**
     * Map refund reason to PayMongo accepted reasons
     */
    private function mapRefundReason($reason)
    {
        $reasonMap = [
            'Guest dissatisfaction' => 'requested_by_customer',
            'Service issues' => 'requested_by_customer',
            'Booking error' => 'duplicate',
            'Duplicate payment' => 'duplicate',
            'Cancellation' => 'requested_by_customer',
            'Other' => 'requested_by_customer'
        ];

        return $reasonMap[$reason] ?? 'requested_by_customer';
    }

    /**
     * Validate if PayMongo refund is possible
     */
    public function canRefund(Payment $payment, $amount)
    {
        // Check if payment method is PayMongo
        if (strtolower($payment->payment_method) !== 'paymongo') {
            return [
                'can_refund' => false,
                'message' => 'Payment was not made through PayMongo'
            ];
        }

        // Check if amount is valid
        if ($amount <= 0 || $amount > $payment->amount) {
            return [
                'can_refund' => false,
                'message' => 'Invalid refund amount'
            ];
        }

        // Check if payment can be refunded (not already fully refunded)
        if (!$payment->canBeRefunded()) {
            return [
                'can_refund' => false,
                'message' => 'Payment has already been fully refunded'
            ];
        }

        return [
            'can_refund' => true,
            'message' => 'Payment can be refunded'
        ];
    }
}
