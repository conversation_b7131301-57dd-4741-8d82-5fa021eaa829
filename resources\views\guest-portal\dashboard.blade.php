@extends('guest-portal.layout')

@section('title', 'Dashboard')

@section('header')
    <div class="row align-items-center">
        <div class="col">
            <h1 class="display-5 fw-bold mb-2">Welcome Back!</h1>
            <p class="lead mb-0">Manage your bookings and payments</p>
        </div>
        <div class="col-auto">
            <a href="{{ route('guest-portal.book', $hotel->slug) }}" class="btn btn-light btn-lg">
                <i class="fas fa-plus me-2"></i>New Booking
            </a>
        </div>
    </div>
@endsection

@section('content')
<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-hotel-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">Total Bookings</h5>
                        <h2 class="mb-0">{{ $bookings->count() }}</h2>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-bed fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">Paid Invoices</h5>
                        <h2 class="mb-0">{{ $invoices->where('status', 'paid')->count() }}</h2>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">Pending Payments</h5>
                        <h2 class="mb-0">{{ $invoices->where('status', '!=', 'paid')->count() }}</h2>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Bookings -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bed me-2"></i>Your Bookings
                </h5>
                <a href="{{ route('guest-portal.book', $hotel->slug) }}" class="btn btn-hotel-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>New Booking
                </a>
            </div>
            <div class="card-body">
                @if($bookings->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Booking #</th>
                                    <th>Room</th>
                                    <th>Check-in</th>
                                    <th>Check-out</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($bookings->take(5) as $booking)
                                <tr>
                                    <td><strong>#{{ $booking->id }}</strong></td>
                                    <td>
                                        {{ $booking->room->room_number ?? 'N/A' }}
                                        @if($booking->room->roomType)
                                            <br><small class="text-muted">{{ $booking->room->roomType->name }}</small>
                                        @endif
                                        @if($booking->guest_count)
                                            <br><small class="text-info">{{ $booking->guest_count }} guest{{ $booking->guest_count > 1 ? 's' : '' }}</small>
                                        @endif
                                    </td>
                                    <td>{{ \Carbon\Carbon::parse($booking->checkin_date)->format('M d, Y') }}</td>
                                    <td>{{ \Carbon\Carbon::parse($booking->checkout_date)->format('M d, Y') }}</td>
                                    <td>
                                        @php
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'booked' => 'info',
                                                'checked_in' => 'success',
                                                'checked_out' => 'secondary',
                                                'finished' => 'success'
                                            ];
                                        @endphp
                                        <span class="badge bg-{{ $statusColors[$booking->booking_status] ?? 'secondary' }}">
                                            {{ ucfirst(str_replace('_', ' ', $booking->booking_status)) }}
                                        </span>
                                    </td>
                                    <td>
                                        ₱{{ number_format($booking->total_price, 2) }}
                                        @if($booking->extra_guest_charge > 0)
                                            <br><small class="text-muted">+ ₱{{ number_format($booking->extra_guest_charge, 2) }} extra guests</small>
                                            <br><strong>Total: ₱{{ number_format($booking->total_price + $booking->extra_guest_charge, 2) }}</strong>
                                        @endif
                                    </td>
                                    <td>
                                        @if($booking->invoices->count() > 0)
                                            @foreach($booking->invoices as $invoice)
                                                <a href="{{ route('guest-portal.invoice', [$hotel->slug, $invoice->id]) }}" 
                                                   class="btn btn-sm btn-outline-primary me-1">
                                                    <i class="fas fa-file-invoice"></i>
                                                </a>
                                            @endforeach
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($bookings->count() > 5)
                        <div class="text-center mt-3">
                            <p class="text-muted">Showing 5 of {{ $bookings->count() }} bookings</p>
                        </div>
                    @endif
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-bed fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No bookings found</h5>
                        <p class="text-muted">You haven't made any bookings yet.</p>
                        <a href="{{ route('guest-portal.book', $hotel->slug) }}" class="btn btn-hotel-primary">
                            <i class="fas fa-plus me-2"></i>Make Your First Booking
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Invoices & Payments -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>Recent Invoices
                </h5>
            </div>
            <div class="card-body">
                @if($invoices->count() > 0)
                    @foreach($invoices->take(5) as $invoice)
                    <div class="d-flex justify-content-between align-items-center mb-3 pb-3 border-bottom">
                        <div>
                            <h6 class="mb-1">Invoice #{{ $invoice->id }}</h6>
                            <small class="text-muted">
                                {{ \Carbon\Carbon::parse($invoice->invoice_date)->format('M d, Y') }}
                            </small>
                            <br>
                            <span class="badge bg-{{ $invoice->status === 'paid' ? 'success' : 'warning' }}">
                                {{ ucfirst($invoice->status) }}
                            </span>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">₱{{ number_format($invoice->total_amount, 2) }}</div>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('guest-portal.invoice', [$hotel->slug, $invoice->id]) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @if($invoice->status !== 'paid')
                                    <a href="{{ route('guest-portal.payment', [$hotel->slug, $invoice->id]) }}" 
                                       class="btn btn-hotel-primary btn-sm">
                                        <i class="fas fa-credit-card"></i>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                    
                    @if($invoices->count() > 5)
                        <div class="text-center">
                            <small class="text-muted">{{ $invoices->count() - 5 }} more invoices...</small>
                        </div>
                    @endif
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-file-invoice fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No invoices found</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
