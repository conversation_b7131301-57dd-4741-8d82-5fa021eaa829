<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Room;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\RoomType;
use App\Models\Guest;
use App\Models\Invoice;
use App\Models\Hotel;
use App\Models\AdditionalCharge;
use Carbon\Carbon;

class BookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $bookings = Booking::with('room', 'invoices')->get();
        return view('admin.bookings.view-bookings', compact('bookings'));
    }

    public function showBooking() {}

    /**
     * Show the form for creating a new resource.
     */
    public function getAvailableRoomsforRestaurant(Request $request)
    {
        // Fetch all rooms that have guests currently checked in
        $rooms = Room::with(['roomType', 'bookings' => function ($query) {
                $query->where('booking_status', 'checked_in');
            }])
            ->whereHas('bookings', function ($query) {
                $query->where('booking_status', 'checked_in'); // Only consider rooms that are checked in
            })
            ->get();

        // Map room data to a format suitable for the frontend
        $roomData = $rooms->map(function ($room) {
            $currentBooking = $room->bookings->first(); // Assuming there is only one active "checked_in" booking per room
            return [
                'id' => $room->id,
                'number' => $room->room_number,
                'type' => $room->roomType ? $room->roomType->name : 'N/A', // Handle cases where roomType might be null
                'guest_name' => $currentBooking ? $currentBooking->guest_first_name . ' ' . $currentBooking->guest_last_name : 'N/A', // Add guest name
            ];
        });

        // Return the response as JSON
        return response()->json(['rooms' => $roomData]);
    }







    public function getAvailableRooms(Request $request)
    {
        $checkinDate = $request->input('checkin_date');
        $checkoutDate = $request->input('checkout_date');

        // Fetch rooms that do not have any overlapping bookings within the requested date range
        $rooms = Room::with('roomType')
            ->whereDoesntHave('bookings', function ($query) use ($checkinDate, $checkoutDate) {
                $query->where(function ($query) use ($checkinDate, $checkoutDate) {
                    $query->whereBetween('checkin_date', [$checkinDate, $checkoutDate])
                        ->orWhereBetween('checkout_date', [$checkinDate, $checkoutDate])
                        ->orWhere(function ($query) use ($checkinDate, $checkoutDate) {
                            $query->where('checkin_date', '<=', $checkinDate)
                                ->where('checkout_date', '>=', $checkoutDate);
                        });
                });
            })
            ->get();

        return response()->json($rooms);
    }


    public function add()
    {
        $guests = Guest::all();
        $room_types = RoomType::all();
        return view('admin.bookings.add-bookings', compact('room_types', 'guests'));
    }

    /**
     * Store a newly created resource in storage.
     */

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'guest_first_name' => 'required|string|max:255',
            'guest_last_name' => 'required|string|max:255',
            'guest_email' => 'nullable|email',
            'guest_phone' => 'nullable|string',
            'guest_count' => 'required|integer|min:1|max:20',
            'guest_id' => 'nullable|numeric',//vince added
            'room_id' => 'required|exists:rooms,id',
            'checkin_date' => 'required|date_format:Y-m-d H:i',
            'checkout_date' => 'required|date_format:Y-m-d H:i',
            'total_price' => 'required|numeric|min:0',
            'booking_status' => 'required|string|in:pending,booked,checked_in,checked_out,finished',
            'booked_by' => 'required|string|max:255',
            'cancelled_by' => 'nullable|string|max:255',
            'checked_in_by' => 'nullable|string|max:255',
            'checked_out_by' => 'nullable|string|max:255'
        ]);

        try {
            // Convert the dates to the MySQL compatible format
            $validatedData['checkin_date'] = Carbon::createFromFormat('Y-m-d H:i', $validatedData['checkin_date'])->format('Y-m-d H:i:s');
            $validatedData['checkout_date'] = Carbon::createFromFormat('Y-m-d H:i', $validatedData['checkout_date'])->format('Y-m-d H:i:s');

            // Check if the room is already booked during the requested dates
            $room = Room::with('roomType')->find($validatedData['room_id']);

            if ($room->is_booked) {
                $existingBooking = Booking::where('room_id', $validatedData['room_id'])
                    ->where(function ($query) use ($validatedData) {
                        $query->whereBetween('checkin_date', [$validatedData['checkin_date'], $validatedData['checkout_date']])
                            ->orWhereBetween('checkout_date', [$validatedData['checkin_date'], $validatedData['checkout_date']])
                            ->orWhere(function ($query) use ($validatedData) {
                                $query->where('checkin_date', '<=', $validatedData['checkin_date'])
                                    ->where('checkout_date', '>=', $validatedData['checkout_date']);
                            });
                    })
                    ->exists();

                if ($existingBooking) {
                    return redirect()->back()->with('error', 'The selected room is already booked during the requested dates.');
                }
            }

            // Check room capacity and calculate extra guest charges
            $guestCount = $validatedData['guest_count'];
            $roomType = $room->roomType;

            if (!$roomType->canAccommodate($guestCount)) {
                return redirect()->back()->with('error', 'The selected room can accommodate a maximum of ' . $roomType->max_capacity . ' guests.');
            }

            // Calculate extra guest charges
            $extraGuests = $roomType->getExtraGuestCount($guestCount);
            $extraGuestCharge = 0;

            if ($extraGuests > 0) {
                $hotel = Hotel::find($room->hotel_id);
                if ($hotel && $hotel->enable_extra_guest_charges) {
                    $checkinDate = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $validatedData['checkin_date']);
                    $checkoutDate = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $validatedData['checkout_date']);
                    $nights = $checkinDate->diffInDays($checkoutDate);

                    if ($hotel->extra_guest_charge_type === 'per_night') {
                        $extraGuestCharge = $extraGuests * $hotel->extra_guest_charge_amount * $nights;
                    } else {
                        $extraGuestCharge = $extraGuests * $hotel->extra_guest_charge_amount;
                    }
                }
            }

            $validatedData['extra_guests'] = $extraGuests;
            $validatedData['extra_guest_charge'] = $extraGuestCharge;

            // Check if Guest already exists WHERE first_name, last_name, email, phone
            $guest = Guest::where('first_name', $validatedData['guest_first_name'])
                ->where('last_name', $validatedData['guest_last_name'])
                //vince->where('email', $validatedData['guest_email'])
                ->where('phone', $validatedData['guest_phone'])
                ->first();
            if (!$guest) {
                $guest = new Guest();
                $guest->first_name = $validatedData['guest_first_name'];
                $guest->last_name = $validatedData['guest_last_name'];
                $guest->email = $validatedData['guest_email'];
                $guest->phone = $validatedData['guest_phone'];
                $guest->save();
            }
            $validatedData['guest_id'] = $guest->id;//vince added
            $validatedData['booked_by'] = Auth::user()->name;//vince added
            $validatedData['last_updated_by'] = Auth::user()->name;//vince added
            // Proceed with booking creation
            $booking = new Booking();
            $booking->fill($validatedData);
            $booking->save();

            // Log activity
            log_activity('Created a new booking for room ' . $booking->room_id, 'Booking Module');

            // Update room booking status to booked
            $room->is_booked = true;
            $room->booked_at = $validatedData['checkin_date'];
            $room->booked_until = $validatedData['checkout_date'];
            $room->save();

            // Auto create Invoice
            $invoice = Invoice::create([
                'booking_id' => $booking->id,
                'total_amount' => $validatedData['total_price'] + $extraGuestCharge,
                'invoice_date' => now(),
                'status' => 'unpaid',
                'created_by' => Auth::user()->name
            ]);

            // Add extra guest charge as additional charge if applicable
            if ($extraGuestCharge > 0) {
                AdditionalCharge::create([
                    'invoice_id' => $invoice->id,
                    'description' => "Extra guest charge ({$extraGuests} extra guest" . ($extraGuests > 1 ? 's' : '') . ")",
                    'amount' => $extraGuestCharge,
                ]);
            }
            return redirect()->route('admin.booking.edit', $booking->id)->with('success', 'Booking created successfully.');
            //vince orig return redirect()->route('admin.bookings')->with('success', 'Booking created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to create booking: ' . $e->getMessage());
        }
    }




    /**
     * Display the specified resource.
     */
    public function show(Booking $booking)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $booking = Booking::find($id);
        if ($booking) {
            $room_types = RoomType::all();
            $rooms = Room::all();
            return view('admin.bookings.edit-bookings', with(['booking' => $booking, 'rooms' => $rooms, 'room_types' => $room_types]));
        } else {
            return redirect()->route('admin.bookings')->with('error', 'Booking is not found');
        }
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'guest_first_name' => 'required|string|max:255',
            'guest_last_name' => 'required|string|max:255',
            'guest_email' => 'nullable|email',
            'guest_phone' => 'nullable|string',
            'guest_count' => 'required|integer|min:1|max:20',
            'guest_id' => 'nullable|numeric',//vince added
            'room_id' => 'required|exists:rooms,id',
            'checkin_date' => 'required|date_format:Y-m-d H:i',
            'checkout_date' => 'required|date_format:Y-m-d H:i',
            'total_price' => 'required|numeric|min:0',
            'booking_status' => 'required|string|in:pending,booked,checked_in,checked_out,finished',
            'notes' => 'nullable|string'//vince added
        ]);

        try {
            // Convert the dates to the MySQL compatible format
            $validatedData['checkin_date'] = Carbon::createFromFormat('Y-m-d H:i', $validatedData['checkin_date'])->format('Y-m-d H:i:s');
            $validatedData['checkout_date'] = Carbon::createFromFormat('Y-m-d H:i', $validatedData['checkout_date'])->format('Y-m-d H:i:s');

            $booking = Booking::findOrFail($id);
            //vince added block
            $username = Auth::user()->name; // Get the logged-in user's name
            if (!empty($validatedData['notes'])) {
                $todayDate = Carbon::now()->format('F j, Y'); // Get today's date
                $existingNotes = $booking->notes ? $booking->notes . "\n" : ''; // Add a newline if there are existing notes
                $validatedData['notes'] = $existingNotes . $validatedData['notes'] . ' - '.$username.' ('.$todayDate.')';
            }//vince end
            // Check if the room is already booked during the requested dates
            $room = Room::with('roomType')->find($validatedData['room_id']);

            if ($room->is_booked && $room->id !== $booking->room_id) {
                $existingBooking = Booking::where('room_id', $validatedData['room_id'])
                    ->where(function ($query) use ($validatedData) {
                        $query->whereBetween('checkin_date', [$validatedData['checkin_date'], $validatedData['checkout_date']])
                            ->orWhereBetween('checkout_date', [$validatedData['checkin_date'], $validatedData['checkout_date']])
                            ->orWhere(function ($query) use ($validatedData) {
                                $query->where('checkin_date', '<=', $validatedData['checkin_date'])
                                    ->where('checkout_date', '>=', $validatedData['checkout_date']);
                            });
                    })
                    ->exists();

                if ($existingBooking) {
                    return redirect()->back()->with('error', 'The selected room is already booked during the requested dates.');
                }
            }

            // Check room capacity and calculate extra guest charges
            $guestCount = $validatedData['guest_count'];
            $roomType = $room->roomType;

            if (!$roomType->canAccommodate($guestCount)) {
                return redirect()->back()->with('error', 'The selected room can accommodate a maximum of ' . $roomType->max_capacity . ' guests.');
            }

            // Calculate extra guest charges
            $extraGuests = $roomType->getExtraGuestCount($guestCount);
            $extraGuestCharge = 0;

            if ($extraGuests > 0) {
                $hotel = Hotel::find($room->hotel_id);
                if ($hotel && $hotel->enable_extra_guest_charges) {
                    $checkinDate = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $validatedData['checkin_date']);
                    $checkoutDate = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $validatedData['checkout_date']);
                    $nights = $checkinDate->diffInDays($checkoutDate);

                    if ($hotel->extra_guest_charge_type === 'per_night') {
                        $extraGuestCharge = $extraGuests * $hotel->extra_guest_charge_amount * $nights;
                    } else {
                        $extraGuestCharge = $extraGuests * $hotel->extra_guest_charge_amount;
                    }
                }
            }

            $validatedData['extra_guests'] = $extraGuests;
            $validatedData['extra_guest_charge'] = $extraGuestCharge;

            // Update specific columns based on booking status
            //vince $userEmail = Auth::check() ? Auth::user()->email : 'no_user';

            switch ($validatedData['booking_status']) {
                case 'booked':
                    $booking->booked_by = $username;//vince orig $userEmail;
                    break;
                case 'cancelled':
                    $booking->cancelled_by = $username;//vince orig $userEmail;
                    break;
                case 'checked_in':
                    $booking->checked_in_by = $username;//vince orig $userEmail;
                    break;
                case 'checked_out':
                    $booking->checked_out_by = $username;//vince orig $userEmail;
                    break;
            }
            $booking->last_updated_by = $username;//vince added
            // Update booking details
            $booking->fill($validatedData);
            $booking->save();

            // Log activity
            log_activity('Updated booking with ID ' . $booking->id, 'Booking Module');

            // Update room booking status if room changed
            if ($booking->room_id != $validatedData['room_id']) {
                // Reset previous room status
                $previousRoom = Room::find($booking->room_id);
                $previousRoom->is_booked = false;
                $previousRoom->booked_at = null;
                $previousRoom->booked_until = null;
                $previousRoom->save();

                // Update new room status
                $room->is_booked = true;
                $room->booked_at = $validatedData['checkin_date'];
                $room->booked_until = $validatedData['checkout_date'];
                $room->save();
            } elseif ($booking->checkin_date !== $validatedData['checkin_date'] || $booking->checkout_date !== $validatedData['checkout_date']) {
                // If dates have changed for the same room
                $room->booked_at = $validatedData['checkin_date'];
                $room->booked_until = $validatedData['checkout_date'];
                $room->save();
            }

            return redirect()->route('admin.booking.edit', $id)->with('success', 'Booking updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update booking: ' . $e->getMessage());
        }
    }



    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Booking $booking)
    {
        return redirect()->route('admin.bookings')->with('error', 'Booking delete is disabled');//vince added
    }
}
