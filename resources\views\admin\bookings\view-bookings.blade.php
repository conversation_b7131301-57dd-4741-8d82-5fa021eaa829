@include('layout.admin-header')

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Bookings</h6>
                            <a href="{{route('admin.booking.add')}}" class="btn btn-primary float-right"><i class="fa fa-plus"></i> Add Booking</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Book ID</th>
                                            <th>Guest Name</th>
                                            <th>Room Number</th>
                                            <th>Room Type</th>
                                            <th>Check In Date</th>
                                            <th>Check Out Date</th>
                                            <th>Booked By</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <!-- Moment -->
                                    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
                                    <tbody>
                                        @foreach ($bookings as $booking)
                                            <tr>
                                                <td>{{ $booking->id }}</td>
                                                <td>@if ($booking->guest_id)
                                                    <a href="{{ route('admin.guest.edit', $booking->guest_id) }}">
                                                        {{ $booking->guest_first_name . ' ' . $booking->guest_last_name }}
                                                    </a>
                                                    @else
                                                        {{ $booking->guest_first_name . ' ' . $booking->guest_last_name }}
                                                    @endif
                                                    @if($booking->guest_count)
                                                        <br><small class="text-info">{{ $booking->guest_count }} guest{{ $booking->guest_count > 1 ? 's' : '' }}</small>
                                                        @if($booking->extra_guests > 0)
                                                            <br><small class="text-warning">+{{ $booking->extra_guests }} extra (₱{{ number_format($booking->extra_guest_charge, 2) }})</small>
                                                        @endif
                                                    @endif
                                                </td>
                                                <td>{{ $booking->room->room_number }}</td>
                                                <td>{{ $booking->room->roomType->name }}</td>
                                                <td class="checkin-date" data-date="{{ $booking->checkin_date }}">
                                                    {{ $booking->checkin_date }}</td>
                                                <td class="checkout-date" data-date="{{ $booking->checkout_date }}">
                                                    {{ $booking->checkout_date }}</td>
                                                <td>{{ $booking->booked_by }}</td>
                                                <td>{{ $booking->booking_status }}</td>

                                                <td>
                                                    <a href="{{ route('admin.booking.edit', $booking->id) }}"
                                                        class="btn btn-primary btn-circle btn-sm" title="Edit Booking">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('admin.booking.destroy', $booking->id) }}"
                                                        method="POST" style="display: inline;" class="deleteForm">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-circle btn-sm deleteButton" title="Delete Booking">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>

                                                    <!-- Edit Invoice Button -->
                                                    <a href="{{ route('admin.invoice.edit',  $booking->invoices->first()->id) }}"
                                                        class="btn btn-info btn-circle btn-sm" title="Edit Invoice">
                                                        <i class="fas fa-file-invoice"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Function to format dates using moment.js
                                        function formatDate(dateString) {
                                            return moment(dateString).format('MMMM D, YYYY h:mm A');
                                        }

                                        // Format all check-in dates
                                        document.querySelectorAll('.checkin-date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });

                                        // Format all check-out dates
                                        document.querySelectorAll('.checkout-date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });
                                    });
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    @include('layout.scripts')

</body>

</html>
