<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('room_types', function (Blueprint $table) {
            $table->integer('max_capacity')->default(2)->after('rate');
            $table->integer('standard_capacity')->default(2)->after('max_capacity');
        });

        // Update existing room types with appropriate capacities
        DB::table('room_types')->where('name', 'Single')->update([
            'max_capacity' => 2,
            'standard_capacity' => 1
        ]);
        
        DB::table('room_types')->where('name', 'Double')->update([
            'max_capacity' => 3,
            'standard_capacity' => 2
        ]);
        
        DB::table('room_types')->where('name', 'Family')->update([
            'max_capacity' => 6,
            'standard_capacity' => 4
        ]);
        
        DB::table('room_types')->where('name', 'Suite')->update([
            'max_capacity' => 8,
            'standard_capacity' => 4
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('room_types', function (Blueprint $table) {
            $table->dropColumn(['max_capacity', 'standard_capacity']);
        });
    }
};
