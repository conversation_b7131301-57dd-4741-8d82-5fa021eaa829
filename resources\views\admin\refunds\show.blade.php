@include('layout.admin-header')
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        Refund Details - {{ $refund->refund_number }}
                                    </h6>
                                    <span class="badge {{ $refund->statusBadgeClass }} badge-lg">
                                        {{ $refund->formattedStatus }}
                                    </span>
                                </div>
                                
                                <div class="card-body">
                                    <!-- Refund Information -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h6 class="font-weight-bold">Refund Information</h6>
                                            <table class="table table-sm table-borderless">
                                                <tr>
                                                    <td><strong>Refund Number:</strong></td>
                                                    <td>{{ $refund->refund_number }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Type:</strong></td>
                                                    <td>
                                                        <span class="badge badge-secondary">
                                                            {{ ucfirst($refund->refund_type) }} Refund
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Amount:</strong></td>
                                                    <td class="font-weight-bold text-success">
                                                        {{ $currencySymbol }}{{ number_format($refund->refund_amount, 2) }}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Original Payment:</strong></td>
                                                    <td>{{ $currencySymbol }}{{ number_format($refund->original_amount, 2) }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Percentage:</strong></td>
                                                    <td>{{ $refund->refundPercentage }}%</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Payment Method:</strong></td>
                                                    <td>
                                                        <span class="badge badge-info">
                                                            {{ ucfirst($refund->payment_method) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <h6 class="font-weight-bold">Guest & Booking Information</h6>
                                            <table class="table table-sm table-borderless">
                                                @if($refund->invoice && $refund->invoice->booking)
                                                    <tr>
                                                        <td><strong>Guest Name:</strong></td>
                                                        <td>
                                                            {{ $refund->invoice->booking->guest_first_name }} 
                                                            {{ $refund->invoice->booking->guest_last_name }}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Email:</strong></td>
                                                        <td>{{ $refund->invoice->booking->guest_email ?? 'N/A' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Phone:</strong></td>
                                                        <td>{{ $refund->invoice->booking->guest_phone ?? 'N/A' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Room:</strong></td>
                                                        <td>{{ $refund->invoice->booking->room->name ?? 'N/A' }}</td>
                                                    </tr>
                                                @endif
                                                <tr>
                                                    <td><strong>Invoice #:</strong></td>
                                                    <td>{{ $refund->invoice_id }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Payment ID:</strong></td>
                                                    <td>{{ $refund->payment_id }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- Refund Reason & Notes -->
                                    <div class="row mb-4">
                                        <div class="col-md-12">
                                            <h6 class="font-weight-bold">Refund Reason</h6>
                                            <div class="alert alert-light">
                                                {{ $refund->reason }}
                                            </div>
                                            
                                            @if($refund->notes)
                                                <h6 class="font-weight-bold">Additional Notes</h6>
                                                <div class="alert alert-light">
                                                    {!! nl2br(e($refund->notes)) !!}
                                                </div>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Provider Response -->
                                    @if($refund->provider_response)
                                        <div class="row mb-4">
                                            <div class="col-md-12">
                                                <h6 class="font-weight-bold">Provider Response</h6>
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <pre class="mb-0">{{ json_encode($refund->provider_response, JSON_PRETTY_PRINT) }}</pre>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Actions Card -->
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                                </div>
                                <div class="card-body">
                                    @if($refund->canBeApproved() && auth()->user()->hasPermission('payments.refund'))
                                        <form method="POST" action="{{ route('admin.refunds.approve', $refund) }}" class="mb-2">
                                            @csrf
                                            <button type="submit" class="btn btn-success btn-block" 
                                                    onclick="return confirm('Are you sure you want to approve this refund?')">
                                                <i class="fas fa-check"></i> Approve Refund
                                            </button>
                                        </form>
                                        
                                        <button class="btn btn-danger btn-block mb-2" onclick="showRejectModal()">
                                            <i class="fas fa-times"></i> Reject Refund
                                        </button>
                                    @endif
                                    
                                    @if($refund->canBeProcessed() && auth()->user()->hasPermission('payments.refund'))
                                        <form method="POST" action="{{ route('admin.refunds.process', $refund) }}" class="mb-2">
                                            @csrf
                                            <button type="submit" class="btn btn-primary btn-block" 
                                                    onclick="return confirm('Are you sure you want to process this refund? This will attempt to refund the payment through the payment provider.')">
                                                <i class="fas fa-cog"></i> Process Refund
                                            </button>
                                        </form>
                                    @endif
                                    
                                    @if($refund->canBeCancelled() && auth()->user()->hasPermission('payments.refund'))
                                        <form method="POST" action="{{ route('admin.refunds.cancel', $refund) }}" class="mb-2">
                                            @csrf
                                            <button type="submit" class="btn btn-secondary btn-block" 
                                                    onclick="return confirm('Are you sure you want to cancel this refund?')">
                                                <i class="fas fa-ban"></i> Cancel Refund
                                            </button>
                                        </form>
                                    @endif
                                    
                                    <a href="{{ route('admin.refunds') }}" class="btn btn-outline-primary btn-block">
                                        <i class="fas fa-arrow-left"></i> Back to Refunds
                                    </a>
                                </div>
                            </div>

                            <!-- Timeline Card -->
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Timeline</h6>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary"></div>
                                            <div class="timeline-content">
                                                <h6 class="timeline-title">Refund Requested</h6>
                                                <p class="timeline-text">
                                                    By: {{ $refund->requester->name ?? $refund->requested_by }}<br>
                                                    {{ $refund->requested_at->format('M d, Y H:i') }}
                                                </p>
                                            </div>
                                        </div>

                                        @if($refund->approved_at)
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-success"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Refund Approved</h6>
                                                    <p class="timeline-text">
                                                        By: {{ $refund->approver->name ?? $refund->approved_by }}<br>
                                                        {{ $refund->approved_at->format('M d, Y H:i') }}
                                                    </p>
                                                </div>
                                            </div>
                                        @endif

                                        @if($refund->rejected_at)
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-danger"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Refund Rejected</h6>
                                                    <p class="timeline-text">
                                                        By: {{ $refund->rejector->name ?? $refund->rejected_by }}<br>
                                                        {{ $refund->rejected_at->format('M d, Y H:i') }}
                                                    </p>
                                                </div>
                                            </div>
                                        @endif

                                        @if($refund->processed_at)
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-info"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Refund Processed</h6>
                                                    <p class="timeline-text">
                                                        By: {{ $refund->processor->name ?? $refund->processed_by }}<br>
                                                        {{ $refund->processed_at->format('M d, Y H:i') }}
                                                    </p>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    @include('layout.admin-logout-modal')

    <!-- Rejection Reason Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reject Refund</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST" action="{{ route('admin.refunds.reject', $refund) }}">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="rejection_reason">Rejection Reason</label>
                            <textarea name="rejection_reason" id="rejection_reason" 
                                      class="form-control" rows="3" required 
                                      placeholder="Please provide a reason for rejecting this refund..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Reject Refund</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @include('layout.admin-scripts')

    <script>
        function showRejectModal() {
            $('#rejectModal').modal('show');
        }
    </script>

    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e3e6f0;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
        }

        .timeline-content {
            padding-left: 10px;
        }

        .timeline-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .timeline-text {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 0;
        }
    </style>

</body>

</html>
