@include('layout.admin-header')

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Hotel Settings</h1>
                    </div>

                    <!-- Hotel Settings Form -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-cog me-2"></i>Extra Guest Charge Settings
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('admin.hotels.update-settings') }}" method="POST">
                                        @csrf
                                        @method('PUT')

                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="enable_extra_guest_charges" 
                                                           name="enable_extra_guest_charges" 
                                                           value="1"
                                                           {{ $hotel->enable_extra_guest_charges ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="enable_extra_guest_charges">
                                                        <strong>Enable Extra Guest Charges</strong>
                                                    </label>
                                                    <small class="form-text text-muted d-block">
                                                        When enabled, guests exceeding room standard capacity will be charged extra fees.
                                                    </small>
                                                </div>
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="extra_guest_charge_amount" class="form-label">
                                                    <strong>Extra Guest Charge Amount</strong>
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text">₱</span>
                                                    <input type="number" 
                                                           class="form-control" 
                                                           id="extra_guest_charge_amount" 
                                                           name="extra_guest_charge_amount" 
                                                           value="{{ old('extra_guest_charge_amount', $hotel->extra_guest_charge_amount) }}"
                                                           step="0.01" 
                                                           min="0" 
                                                           max="9999.99" 
                                                           required>
                                                </div>
                                                <small class="form-text text-muted">
                                                    Amount to charge per extra guest.
                                                </small>
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="extra_guest_charge_type" class="form-label">
                                                    <strong>Charge Type</strong>
                                                </label>
                                                <select class="form-control" 
                                                        id="extra_guest_charge_type" 
                                                        name="extra_guest_charge_type" 
                                                        required>
                                                    <option value="per_night" {{ $hotel->extra_guest_charge_type === 'per_night' ? 'selected' : '' }}>
                                                        Per Night
                                                    </option>
                                                    <option value="per_stay" {{ $hotel->extra_guest_charge_type === 'per_stay' ? 'selected' : '' }}>
                                                        Per Stay (One-time)
                                                    </option>
                                                </select>
                                                <small class="form-text text-muted">
                                                    Choose whether to charge per night or once per stay.
                                                </small>
                                            </div>
                                        </div>

                                        <hr>

                                        <div class="row">
                                            <div class="col-12">
                                                <h6 class="text-primary mb-3">
                                                    <i class="fas fa-info-circle me-2"></i>How Extra Guest Charges Work
                                                </h6>
                                                <div class="alert alert-info">
                                                    <ul class="mb-0">
                                                        <li><strong>Standard Capacity:</strong> Each room type has a standard capacity (e.g., 2 guests for Double room)</li>
                                                        <li><strong>Maximum Capacity:</strong> Each room type also has a maximum capacity (e.g., 4 guests for Double room)</li>
                                                        <li><strong>Extra Charges:</strong> Guests exceeding standard capacity but within maximum capacity will be charged extra</li>
                                                        <li><strong>Example:</strong> Double room (2 standard, 4 max) with 3 guests = 1 extra guest charge</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-2"></i>Save Settings
                                                </button>
                                                <a href="{{ route('admin.index') }}" class="btn btn-secondary ms-2">
                                                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-hotel me-2"></i>Current Hotel
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">{{ $hotel->name }}</h5>
                                    <p class="card-text">{{ $hotel->description }}</p>
                                    <hr>
                                    <p class="mb-1"><strong>Email:</strong> {{ $hotel->email }}</p>
                                    <p class="mb-1"><strong>Phone:</strong> {{ $hotel->phone ?? 'Not set' }}</p>
                                    <p class="mb-0"><strong>Status:</strong> 
                                        <span class="badge badge-{{ $hotel->status === 'active' ? 'success' : 'warning' }}">
                                            {{ ucfirst($hotel->status) }}
                                        </span>
                                    </p>
                                </div>
                            </div>

                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-users me-2"></i>Room Capacity Overview
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Current room type capacities:</p>
                                    @if($hotel->roomTypes && $hotel->roomTypes->count() > 0)
                                        @foreach($hotel->roomTypes as $roomType)
                                            <div class="mb-2">
                                                <strong>{{ $roomType->name }}:</strong><br>
                                                <small class="text-muted">
                                                    Standard: {{ $roomType->standard_capacity }} | 
                                                    Max: {{ $roomType->max_capacity }}
                                                </small>
                                            </div>
                                        @endforeach
                                    @else
                                        <p class="text-muted">No room types configured yet.</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    @include('layout.admin-scripts')
</body>
</html>
