@include('layout.admin-header')
@php
    $currencySymbol = config('currency.symbol');
@endphp

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')
                    
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Refund Management</h6>
                            <div>
                                <button class="btn btn-info btn-sm" onclick="loadRefundStats()">
                                    <i class="fas fa-chart-bar"></i> Statistics
                                </button>
                            </div>
                        </div>
                        
                        <!-- Filters -->
                        <div class="card-body">
                            <form method="GET" action="{{ route('admin.refunds') }}" class="mb-4">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="status">Status</label>
                                        <select name="status" id="status" class="form-control">
                                            <option value="">All Statuses</option>
                                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                            <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Processing</option>
                                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>Failed</option>
                                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="payment_method">Payment Method</label>
                                        <select name="payment_method" id="payment_method" class="form-control">
                                            <option value="">All Methods</option>
                                            <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>Cash</option>
                                            <option value="paymongo" {{ request('payment_method') == 'paymongo' ? 'selected' : '' }}>PayMongo</option>
                                            <option value="paypal" {{ request('payment_method') == 'paypal' ? 'selected' : '' }}>PayPal</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="search">Search</label>
                                        <input type="text" name="search" id="search" class="form-control" 
                                               placeholder="Refund number or guest name" value="{{ request('search') }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary btn-sm">Filter</button>
                                            <a href="{{ route('admin.refunds') }}" class="btn btn-secondary btn-sm">Clear</a>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            <!-- Refunds Table -->
                            <div class="table-responsive">
                                <table class="table table-bordered" id="refundsTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Refund #</th>
                                            <th>Guest</th>
                                            <th>Amount</th>
                                            <th>Payment Method</th>
                                            <th>Reason</th>
                                            <th>Status</th>
                                            <th>Requested Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($refunds as $refund)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('admin.refunds.show', $refund) }}" class="text-primary">
                                                        {{ $refund->refund_number }}
                                                    </a>
                                                </td>
                                                <td>
                                                    @if($refund->invoice && $refund->invoice->booking)
                                                        {{ $refund->invoice->booking->guest_first_name }} 
                                                        {{ $refund->invoice->booking->guest_last_name }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>{{ $currencySymbol }}{{ number_format($refund->refund_amount, 2) }}</td>
                                                <td>
                                                    <span class="badge badge-info">{{ ucfirst($refund->payment_method) }}</span>
                                                </td>
                                                <td>{{ Str::limit($refund->reason, 30) }}</td>
                                                <td>
                                                    <span class="badge {{ $refund->statusBadgeClass }}">
                                                        {{ $refund->formattedStatus }}
                                                    </span>
                                                </td>
                                                <td>{{ $refund->requested_at->format('M d, Y H:i') }}</td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ route('admin.refunds.show', $refund) }}" 
                                                           class="btn btn-sm btn-info" title="View Details">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        
                                                        @if($refund->canBeApproved() && auth()->user()->hasPermission('payments.refund'))
                                                            <button class="btn btn-sm btn-success" 
                                                                    onclick="approveRefund({{ $refund->id }})" title="Approve">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-danger" 
                                                                    onclick="rejectRefund({{ $refund->id }})" title="Reject">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        @endif
                                                        
                                                        @if($refund->canBeProcessed() && auth()->user()->hasPermission('payments.refund'))
                                                            <button class="btn btn-sm btn-primary" 
                                                                    onclick="processRefund({{ $refund->id }})" title="Process">
                                                                <i class="fas fa-cog"></i>
                                                            </button>
                                                        @endif
                                                        
                                                        @if($refund->canBeCancelled() && auth()->user()->hasPermission('payments.refund'))
                                                            <button class="btn btn-sm btn-secondary" 
                                                                    onclick="cancelRefund({{ $refund->id }})" title="Cancel">
                                                                <i class="fas fa-ban"></i>
                                                            </button>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="8" class="text-center">No refunds found</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center">
                                {{ $refunds->appends(request()->query())->links() }}
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    @include('layout.admin-logout-modal')

    <!-- Refund Statistics Modal -->
    <div class="modal fade" id="statsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Refund Statistics</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="statsContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Reason Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reject Refund</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="rejectForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="rejection_reason">Rejection Reason</label>
                            <textarea name="rejection_reason" id="rejection_reason" 
                                      class="form-control" rows="3" required 
                                      placeholder="Please provide a reason for rejecting this refund..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Reject Refund</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @include('layout.admin-scripts')

    <script>
        function loadRefundStats() {
            $('#statsModal').modal('show');
            
            fetch('{{ route("admin.refunds.statistics") }}')
                .then(response => response.json())
                .then(data => {
                    const content = `
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-left-primary">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Total Refunds
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">${data.total_refunds}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-left-warning">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Pending Refunds
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">${data.pending_refunds}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mt-3">
                                <div class="card border-left-success">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Completed Refunds
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">${data.completed_refunds}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mt-3">
                                <div class="card border-left-info">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Total Refunded Amount
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $currencySymbol }}${parseFloat(data.total_refunded_amount).toFixed(2)}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    document.getElementById('statsContent').innerHTML = content;
                })
                .catch(error => {
                    document.getElementById('statsContent').innerHTML = '<div class="alert alert-danger">Failed to load statistics</div>';
                });
        }

        function approveRefund(refundId) {
            if (confirm('Are you sure you want to approve this refund?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/refunds/${refundId}/approve`;
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);
                
                document.body.appendChild(form);
                form.submit();
            }
        }

        function rejectRefund(refundId) {
            document.getElementById('rejectForm').action = `/admin/refunds/${refundId}/reject`;
            $('#rejectModal').modal('show');
        }

        function processRefund(refundId) {
            if (confirm('Are you sure you want to process this refund? This will attempt to refund the payment through the payment provider.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/refunds/${refundId}/process`;
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);
                
                document.body.appendChild(form);
                form.submit();
            }
        }

        function cancelRefund(refundId) {
            if (confirm('Are you sure you want to cancel this refund?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/refunds/${refundId}/cancel`;
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);
                
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>

</body>

</html>
