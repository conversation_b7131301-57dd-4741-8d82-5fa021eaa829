<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class RoomType extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'room_types';
    protected $fillable = ['hotel_id', 'name', 'description', 'rate', 'max_capacity', 'standard_capacity'];

    /**
     * Check if guest count exceeds standard capacity
     */
    public function hasExtraGuests($guestCount)
    {
        return $guestCount > $this->standard_capacity;
    }

    /**
     * Calculate number of extra guests
     */
    public function getExtraGuestCount($guestCount)
    {
        return max(0, $guestCount - $this->standard_capacity);
    }

    /**
     * Check if guest count is within maximum capacity
     */
    public function canAccommodate($guestCount)
    {
        return $guestCount <= $this->max_capacity;
    }
}
