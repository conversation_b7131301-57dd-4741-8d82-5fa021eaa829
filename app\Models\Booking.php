<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Booking extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'bookings';
    protected $fillable = ['hotel_id', 'guest_first_name', 'guest_last_name', 'guest_email', 'guest_phone', 'guest_count', 'extra_guests', 'extra_guest_charge', 'guest_id', 'room_id', 'checkin_date', 'checkout_date', 'total_price', 'booking_status', 'booked_by', 'cancelled_by', 'checked_in_by', 'checked_out_by', 'notes'];
    //vince added guest_id, notes
    public function room()
    {
        return $this->belongsTo(Room::class, 'room_id', 'id');
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function events()
    {
        return $this->hasMany(Event::class);
    }

    /**
     * Calculate total booking amount including extra guest charges
     */
    public function getTotalAmountAttribute()
    {
        return $this->total_price + $this->extra_guest_charge;
    }

    /**
     * Get the number of nights for this booking
     */
    public function getNightsAttribute()
    {
        $checkin = \Carbon\Carbon::parse($this->checkin_date);
        $checkout = \Carbon\Carbon::parse($this->checkout_date);
        return $checkin->diffInDays($checkout);
    }

}
